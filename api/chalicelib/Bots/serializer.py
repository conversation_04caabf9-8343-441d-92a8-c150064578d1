from sqlalchemy.orm.exc import NoResultFound, MultipleResultsFound
from chalicelib.Utils.exceptions import APIException
from chalicelib.Utils.functions import validateSchema, readJsonFile, unixTimeNow
from chalicelib.Bots.models import TradingBot, TradingBotType, Trades, TradeResult
from chalicelib.Portfolios.models import Portfolio, AccountPortfolio
from chalicelib.Bots.Etrade.models import Etrade
from chalicelib.Accounts.models import Account, AccountOwner, AccountBalance
from chalicelib.Accounts.serializer import AccountSerializer
from chalicelib.lambda_resolver import get_lambda_arn
import uuid, traceback, json, requests, os, boto3, time, sys
from chalicelib.Bots.Etrade.serializer import ETradeSerializer
from chalicelib.Bots.Schwab.serializer import SchwabSerializer
from chalicelib.Bots.Robinhood.serializer import RobinhoodSerializer, RobinhoodWrapper
from chalicelib.Bots.SignalStack.serializer import SignalStackSerializer
from chalicelib.Bots.Alpaca.serializer import AlpacaSerializer
from chalicelib.Bots.Alpaca.models import Alpaca
from chalicelib.Bots.helpers import TradeObject
from chalicelib.Subscriptions.serializer import SubscriptionSerializer
from chalicelib.Utils.models import Status
from chalicelib.Bots.Robinhood.schemas import RobinhoodAccounts
from chalicelib.Bots.schemas import BotSchema, TradeSchema, PositionSchema
from termcolor import colored
from datetime import datetime, date, timezone
from dateutil.relativedelta import relativedelta
from chalicelib.Logs.serializer import EventSerializer
from chalicelib.Bots.Robinhood.models import Robinhood
from chalicelib.Reddit.serializer import RedditSerializer
from chalicelib.Mailjet.serializer import MailjetSerializer

class BotSerializer:
    @classmethod
    def _create(cls, data, app):
        db = app.current_request.db
        try:
            account = db.query(Account).filter(Account.uuid == data['account_id']).one()
            data['account_id'] = account.id
        except NoResultFound:
            db.close()
            raise APIException(f"Unable to locate account with id: {data['account_id']}")

        # Check if status_id = 18 exists, fallback to status_id = 4 (Pending) if not
        try:
            status_check = db.query(Status).filter(Status.id == 18).first()
            if status_check:
                status_id = 18  # PENDING_SETUP
            else:
                print("Status ID 18 not found, using status ID 4 (Pending) as fallback")
                status_id = 4  # PENDING (fallback)
        except Exception as e:
            print(f"Error checking status: {e}")
            status_id = 4  # PENDING (fallback)

        newBot = TradingBot(
            uuid=uuid.uuid4().hex,
            account_id=data['account_id'],
            bot_type_id=data['bot_type_id'],
            status_id=status_id
        )

        try:
            db.add(newBot)
            db.commit()
            print(f"Successfully created trading bot with status_id: {status_id}")
        except Exception as e:
            print(f"Database error: {e}")
            print(traceback.format_exc())
            db.rollback()
            raise APIException(f"Unable to create trading bot: {str(e)}")

        ### Create the Etrade bot
        if data['bot_type_id'] == 1:
            botcreated, authorization = ETradeSerializer.create(data['account_id'], newBot.id, data['consumer_key'], data['consumer_secret'], app)
            if botcreated:
                pass
            else:
                try:
                    db.delete(newBot)
                    db.commit()
                    raise APIException("Unable to create trading bot")
                except Exception as e:
                    print(traceback.format_exc())
                    db.rollback()
                    raise APIException("Unable to rollback trading bot upon etrade account connect error")
        ### Create Schwab bot
        elif data['bot_type_id'] == 2:
            authorization = SchwabSerializer.create(data['account_id'], newBot.id, data['consumer_key'], data['consumer_secret'], app)
            if authorization:
                pass
            else:
                try:
                    db.delete(newBot)
                    db.commit()
                    raise APIException("Unable to create trading bot")
                except Exception as e:
                    print(traceback.format_exc())
                    db.rollback()
                    db.close()
                    raise APIException("Unable to rollback trading bot upon schwab account connect error")
        ### Create a Robinhood bot
        elif data['bot_type_id'] == 3:
            botcreated, authorization = RobinhoodSerializer.create(data, newBot, app)
            if botcreated:
                pass
            else:
                try:
                    db.delete(newBot)
                    db.commit()
                    raise APIException("Unable to create trading bot")
                except Exception as e:
                    print(traceback.format_exc())
                    db.rollback()
                    db.close()
                    raise APIException("Unable to rollback trading bot upon Robinhood account connect error")
        ### Create a TradeStation bot
        elif data['bot_type_id'] == 4:
            botcreated, authorization = RobinhoodSerializer.create(data, newBot, app)
            if botcreated:
                pass
            else:
                try:
                    db.delete(newBot)
                    db.commit()
                    raise APIException("Unable to create trading bot")
                except Exception as e:
                    print(traceback.format_exc())
                    db.rollback()
                    db.close()
                    raise APIException("Unable to rollback trading bot upon Robinhood account connect error")
        ### Create a SignalStack bot
        elif data['bot_type_id'] == 5:
            botcreated, authorization = SignalStackSerializer.create(data, newBot, app)
            if botcreated:
                pass
            else:
                try:
                    db.delete(newBot)
                    db.commit()
                    raise APIException("Unable to create trading bot")
                except Exception as e:
                    print(traceback.format_exc())
                    db.rollback()
                    db.close()
                    raise APIException("Unable to rollback trading bot upon Robinhood account connect error")

        ### Create a SignalStack bot
        elif data['bot_type_id'] == 6:
            botcreated, authorization = AlpacaSerializer.create(data, newBot, app)
            if botcreated:
                pass
            else:
                try:
                    db.delete(newBot)
                    db.commit()
                    raise APIException("Unable to create trading bot")
                except Exception as e:
                    print(traceback.format_exc())
                    db.rollback()
                    db.close()
                    raise APIException("Unable to rollback trading bot upon Robinhood account connect error")

        RedditSerializer._track(eventType="New Automation", uniqueId=newBot.uuid)
        return newBot, authorization


    @classmethod
    def create(cls, data, app):
        # Validate incoming request
        validationSchema = readJsonFile('/Bots/RequestJson/createBot.json')
        validateSchema(data, validationSchema)

        bot, authorization = cls._create(data, app)
        db = app.current_request.db
        try:
            account_owner = db.query(AccountOwner.owner_account_id).filter(AccountOwner.account_id == bot.account_id).one()
            account_owner = account_owner[0]
        except NoResultFound:
            account_owner = 1
        ## Don't create subscription during initial setup - only when automation is completed
        ## This will be handled in the assign_brokerage_account method when setup is finalized

        response = {}
        response['success'] = True
        response['bot'] = BotSchema(bot, authorization=authorization)
        return response


    @classmethod
    def _update(cls, data, app):
        db = app.current_request.db
        try:
            account = db.query(Account).filter(Account.uuid == data['account_uuid']).one()
            data['account_id'] = account.id
        except NoResultFound:
            db.close()
            raise APIException(f"Unable to locate account with id: {data['account_uuid']}")

        try:
            tbot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()
        except NoResultFound:
            db.close()
            raise APIException(f"Unable to locate automation with id: {data['bot_uuid']}")

        authorization = None

        if data['reauthorize']:
            ### Create the Etrade bot
            if tbot.bot_type_id == 1:
                botcreated, authorization = ETradeSerializer.reauthorize(data['account_id'], tbot, data['consumer_key'], data['consumer_secret'], app)

            ### Create Schwab bot
            elif tbot.bot_type_id == 2:
                authorization = SchwabSerializer.reauthorize(data['account_id'], tbot.id, data['consumer_key'], data['consumer_secret'], app)

            ### Create a Robinhood bot
            elif tbot.bot_type_id == 3:
                botcreated, authorization = RobinhoodSerializer.reauthorize(data, tbot, app)
                if botcreated:
                    try:
                        tbot.status_id = 1
                        db.commit()
                    except:
                        print(traceback.format_exc())
                        db.rollback()
                        raise APIException("Unable to update status of automation")

            ### Create a TradeStation bot
            elif tbot.bot_type_id == 4:
                botcreated, authorization = RobinhoodSerializer.create(data, tbot, app)

            ### Create a SignalStack bot
            elif tbot.bot_type_id == 5:
                botcreated, authorization = SignalStackSerializer.update(data, tbot, app)

        return tbot, authorization


    @classmethod
    def update(cls, data, app):
        # Validate incoming request
        validationSchema = readJsonFile('/Bots/RequestJson/updateBot.json')
        validateSchema(data, validationSchema)

        bot, authorization = cls._update(data, app)

        response = {}
        response['success'] = True
        response['bot'] = BotSchema(bot, authorization=authorization)
        return response


    @classmethod
    def _delete(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            tbot, account = db.query(TradingBot, Account).join(Account, Account.id == TradingBot.account_id).filter(TradingBot.uuid == bot_uuid).one()
            account_uuid = account.uuid

            # Store original status to determine if subscription cleanup is needed
            original_status = tbot.status_id

            # If automation is incomplete (status_id = 18 or 4), completely remove it
            if tbot.status_id == 18 or tbot.status_id == 4:
                db.delete(tbot)
            else:
                # For completed automations, just mark as deleted
                tbot.status_id = 8
                tbot.brokerage_connected = False
        except NoResultFound:
            print(traceback.format_exc())
            db.close()
            raise APIException(f"Unable to locate bot with uuid: {bot_uuid}")
        except Exception as e:
            print(colored(e, "red"))
            print(traceback.format_exc())
            db.close()
            raise APIException(f"Error when deleting bot")

        try:
            db.commit()
        except Exception as e:
            print(traceback.format_exc())
            db.rollback()
            db.close()
            raise APIException("Unable to delete automation")

        ## Only check for subscription cancellation if this was a completed automation
        ## Incomplete automations (status_id 18 or 4) never had subscriptions created
        if original_status not in [18, 4]:
            ## Check for any existing active automations. If none are still active, cancel the subscription.
            num = db.query(TradingBot).filter(TradingBot.status_id == 1, TradingBot.account_id == account.id).count()
            if num == 0:
                sub = SubscriptionSerializer._get(account.id, app, ACTIVE_ONLY=True)
                if sub:
                    SubscriptionSerializer._delete(sub, app)
        return cls._get(account_uuid, app)


    @classmethod
    def delete(cls, bot_uuid, app):
        bots = cls._delete(bot_uuid, app)
        response = {}
        response['success'] = True
        response['automations'] = [BotSchema(bot) for bot in bots]
        return response

    @classmethod
    def _cleanup_incomplete_automations(cls, app):
        """
        Clean up incomplete automations that are older than 1 hour
        This prevents the database from filling up with abandoned setups
        """
        from datetime import datetime, timedelta

        db = app.current_request.db
        cutoff_time = datetime.utcnow() - timedelta(hours=1)

        try:
            # Find incomplete automations older than 1 hour
            incomplete_bots = db.query(TradingBot).filter(
                TradingBot.status_id.in_([18, 4]),  # PENDING_SETUP or PENDING
                TradingBot.created_at < cutoff_time
            ).all()

            cleanup_count = 0
            for bot in incomplete_bots:
                try:
                    # Delete associated broker-specific records
                    if bot.bot_type_id == 6:  # Alpaca
                        alpaca_records = db.query(Alpaca).filter(Alpaca.trading_bot_id == bot.id).all()
                        for record in alpaca_records:
                            db.delete(record)
                    elif bot.bot_type_id == 1:  # ETrade
                        etrade_records = db.query(Etrade).filter(Etrade.trading_bot_id == bot.id).all()
                        for record in etrade_records:
                            db.delete(record)
                    elif bot.bot_type_id == 3:  # Robinhood
                        rh_records = db.query(Robinhood).filter(Robinhood.trading_bot_id == bot.id).all()
                        for record in rh_records:
                            db.delete(record)

                    # Delete the main bot record
                    db.delete(bot)
                    cleanup_count += 1

                except Exception as e:
                    print(f"Error cleaning up bot {bot.uuid}: {e}")
                    continue

            if cleanup_count > 0:
                db.commit()
                print(f"Cleaned up {cleanup_count} incomplete automations")

            return cleanup_count

        except Exception as e:
            print(f"Error during cleanup: {e}")
            db.rollback()
            return 0

    @classmethod
    def cleanup_incomplete_automations(cls, app):
        """Public method for cleanup endpoint"""
        count = cls._cleanup_incomplete_automations(app)
        response = {}
        response['success'] = True
        response['cleaned_up'] = count
        response['message'] = f"Cleaned up {count} incomplete automations"
        return response


    @classmethod
    def _disable(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            tbot, account = db.query(TradingBot, Account).join(Account, Account.id == TradingBot.account_id).filter(TradingBot.uuid == bot_uuid).one()
            account_uuid = account.uuid
            tbot.status_id = 2
        except NoResultFound:
            print(traceback.format_exc())
            db.close()
            raise APIException(f"Unable to locate bot with uuid: {bot_uuid}")
        except Exception as e:
            print(colored(e, "red"))
            print(traceback.format_exc())
            db.close()
            raise APIException(f"Error when disabling automation")

        try:
            db.commit()
        except Exception as e:
            print(traceback.format_exc())
            db.rollback()
            db.close()
            raise APIException("Unable to disable automation")
        return cls._get(account_uuid, app)


    @classmethod
    def disable(cls, bot_uuid, app):
        bots = cls._disable(bot_uuid, app)
        response = {}
        response['success'] = True
        response['automations'] = [BotSchema(bot) for bot in bots]
        return response


    @classmethod
    def _enable(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            tbot, account = db.query(TradingBot, Account).join(Account, Account.id == TradingBot.account_id).filter(TradingBot.uuid == bot_uuid).one()
            account_uuid = account.uuid
            tbot.status_id = 1
        except NoResultFound:
            print(traceback.format_exc())
            db.close()
            raise APIException(f"Unable to locate bot with uuid: {bot_uuid}")
        except Exception as e:
            print(colored(e, "red"))
            print(traceback.format_exc())
            db.close()
            raise APIException(f"Error when enabling automation")
        print(colored(tbot.status_id, "green"))
        try:
            db.commit()
        except Exception as e:
            print(traceback.format_exc())
            db.rollback()
            raise APIException("Unable to enable automation")
        return cls._get(account_uuid, app)


    @classmethod
    def enable(cls, bot_uuid, app):
        bots = cls._enable(bot_uuid, app)
        response = {}
        response['success'] = True
        response['automations'] = [BotSchema(bot) for bot in bots]
        return response


    @classmethod
    def _auth_bot(cls, data, app):
        db = app.current_request.db

        authenticated = False

        try:
            tbot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()
        except NoResultFound:
            print(traceback.format_exc())
            raise APIException(f"Unable to locate bot with uuid: {data['bot_uuid']}")
        except Exception as e:
            print(colored(e, "red"))
            print(traceback.format_exc())
            raise APIException(f"Error when authorizing bot")

        if tbot.bot_type_id == 1:
            authenticated = ETradeSerializer.authorize(data, tbot, app)
        elif tbot.bot_type_id == 2:
            authenticated = SchwabSerializer.getAccessToken(data, tbot, app)
        elif tbot.bot_type_id == 3:
            authenticated = RobinhoodSerializer.authenticate(tbot, app, DATA=data)
        elif tbot.bot_type_id == 4:
            authenticated = ""
        elif tbot.bot_type_id == 5:
            raise APIException('SignalStack does not require additional authorization.')
        elif tbot.bot_type_id == 6:
            authenticated = True

        if authenticated:
            tbot.status_id = 1
            tbot.brokerage_connected = True
        else:
            tbot.status_id = 15
            tbot.brokerage_connected = False

        try:
            db.commit()
        except:
            print(traceback.format_exc())
            db.rollback()
            raise APIException("Access token was activated but status not updated")
        return tbot


    @classmethod
    def authorizeBot(cls, app, DATA=None, BOT_TYPE_ID=None):
        if DATA:
            data = DATA

            # Validate incoming request
            tbot = cls._auth_bot(data, app)

            response = {}
            response['success'] = True
            response['bot'] = BotSchema(tbot)
        else:
            db = app.current_request.db
            data = {}
            # if BOT_TYPE_ID:
            #     account_uuid = app.current_request.query_params['id']
            #     try:
            #         bots = db.query(TradingBot).join(Account).filter(Account.uuid == account_uuid, TradingBot.status_id == 4, TradingBot.bot_type_id == int(BOT_TYPE_ID)).one()
            #     except NoResultFound:
            #         raise APIException("Unable to locate pending Etrade automation")
            response = {}
            response['success'] = True
        return response


    @classmethod
    def _renew_token(cls, bot_uuid, app):
        db = app.current_request.db

        try:
            tbot = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            print(traceback.format_exc())
            raise APIException(f"Unable to locate bot with uuid: {bot_uuid}")
        except Exception as e:
            print(colored(e, "red"))
            print(traceback.format_exc())
            raise APIException(f"Error when authorizing bot")

        if tbot.bot_type_id == 1:
            renewed, status_code = ETradeSerializer.renewAccessToken(tbot, app)
        elif tbot.bot_type_id == 2:
            renewed, status_code = SchwabSerializer.getAccessToken(tbot, app)
        elif tbot.bot_type_id == 3:
            status_code, renewed = RobinhoodSerializer.authenticate(tbot, app)
        elif tbot.bot_type_id == 4:
            renewed, status_code = RobinhoodSerializer.authenticate(tbot, app)
        elif tbot.bot_type_id == 5:
            raise APIException('SignalStack does not require additional authorization.')
        elif tbot.bot_type_id == 6:
            raise APIException('Alpaca does not require additional authorization.')
        # print("status: ", colored(f"{status_code}", "yellow"))
        # print("renewed: ", colored(f"{renewed}", "yellow"))
        if status_code == 401:
            tbot.status_id = 15
            tbot.brokerage_connected = False

        else:
            tbot.status_id = 1
            tbot.brokerage_connected = True

        try:
            db.commit()
        except:
            print(traceback.format_exc())
            db.rollback()
            raise APIException("Access token could not be renewed and status not updated")
        return tbot, renewed


    @classmethod
    def renewBotToken(cls, bot_uuid, app):
        # Validate incoming request
        tbot, renewed = cls._renew_token(bot_uuid, app)

        response = {}
        if renewed:
            response['success'] = True
            response['bot'] = BotSchema(tbot)
        else:
            response['success'] = False
            response['message'] = "Unable to renew access tokens."
        return response


    @classmethod
    def _get_accounts(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            auto = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        if auto.bot_type_id == 1:
            accounts = ETradeSerializer.getAccounts(auto, app)
        elif auto.bot_type_id == 2:
            accounts = ""
        elif auto.bot_type_id == 3:
            acct, total_balance, available_cash = RobinhoodSerializer.account(auto, app)
            accounts = RobinhoodAccounts(acct)
        elif auto.bot_type_id == 4:
            accounts = ""
        elif auto.bot_type_id == 5:
            raise APIException("SignalStack does not return account lists to Thriving automations")
        elif auto.bot_type_id == 6:
            accounts = [AlpacaSerializer.account(auto, app)]
        return accounts


    @classmethod
    def get_accounts(cls, bot_uuid, app):
        accounts = cls._get_accounts(bot_uuid, app)
        res = {}
        res['success'] = True
        res['accounts'] = accounts
        return res


    @classmethod
    def _otp(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            auto = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        if auto.bot_type_id == 1:
            raise APIException("OTP not available for Etrade accounts")
        elif auto.bot_type_id == 2:
            raise APIException("OTP not available for Schwab accounts")
        elif auto.bot_type_id == 3:
            rh = db.query(Robinhood).filter(Robinhood.trading_bot_id == auto.id).one()
            otp = RobinhoodWrapper.otp(rh.otp_key)
        elif auto.bot_type_id == 4:
            raise APIException("OTP not available for Tradestation accounts")
        elif auto.bot_type_id == 5:
            raise APIException("OTP not available for Signal Stack accounts")
        return otp


    @classmethod
    def otp(cls, bot_uuid, app):
        otp = cls._otp(bot_uuid, app)
        res = {}
        res['success'] = True
        res['otp'] = otp
        return res


    @classmethod
    def _assign_brokerage_account(cls, bot_uuid, data, app):
        db = app.current_request.db
        try:
            auto = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        # Get account owner to determine subscription type
        try:
            account_owner = db.query(AccountOwner.owner_account_id).filter(AccountOwner.account_id == auto.account_id).one()
            account_owner = account_owner[0]
        except NoResultFound:
            account_owner = 1

        # Create subscription if account doesn't have one (only for completed setups)
        subscription = SubscriptionSerializer._get(auto.account_id, app, ACTIVE_ONLY=True)
        if subscription == None:
            if account_owner == 1:
                subscription = SubscriptionSerializer._create(auto.account, app)
                try:
                    templateData = {}
                    templateData['from_email'] = "<EMAIL>"
                    templateData['from_name'] = "Thriving"
                    templateData['template_id'] = "6689605"
                    templateData['variables'] = {
                        "name": auto.account.name,
                        "sub_id": subscription.uuid
                    }
                    templateData['to_email'] = auto.account.email
                    templateData['to_name'] = f"""{auto.account.name}"""
                    templateData['subject'] = "Thriving Automation Subscription"
                    MailjetSerializer.sendTransactionalTemplate(templateData)
                except:
                    print(traceback.format_exc())
                    SubscriptionSerializer._delete(subscription, app)
                    raise APIException("Unable to create subscription. Please contact Thriving support for assistance: <EMAIL>")
            else:
                try:
                    templateData = {}
                    templateData['from_email'] = "<EMAIL>"
                    templateData['from_name'] = "The Innova Team"
                    templateData['template_id'] = "6860025"
                    templateData['variables'] = {
                        "first_name": auto.account.name,
                        "link": "https://dashboard.innovatrade.ai/portfolio-builder"
                    }
                    templateData['to_email'] = auto.account.email
                    templateData['to_name'] = f"""{auto.account.name}"""
                    templateData['subject'] = "Innova Automation Subscription"
                    MailjetSerializer.sendTransactionalTemplate(templateData)
                except:
                    print(traceback.format_exc())
                    raise APIException("Unable to create subscription. Please contact Innova support for assistance: <EMAIL>")

        # Assign brokerage account based on bot type
        if auto.bot_type_id == 1:
            assigned = ETradeSerializer.assign_account(auto, data, app)
        elif auto.bot_type_id == 2:
            assigned = ""
        elif auto.bot_type_id == 3:
            assigned = ""
        elif auto.bot_type_id == 4:
            assigned = ""
        elif auto.bot_type_id == 5:
            raise APIException("SignalStack does not return account lists to Thriving automations")
        elif auto.bot_type_id == 6:
            # For Alpaca, we don't need to assign a specific account - just mark as completed
            assigned = True

        if assigned == False:
            raise APIException("Unable to assign brokerage account to automation service.")

        try:
            auto.status_id = 1  # 1 = ACTIVE (completed setup)
            auto.brokerage_connected = True
            db.commit()
        except:
            db.rollback()
            raise APIException("Unable to change automation status.")
        return auto


    @classmethod
    def assign_brokerage_account(cls, bot_uuid, data, app):
        # Validate incoming request
        validationSchema = readJsonFile('/Bots/RequestJson/assign_brokerage_account.json')
        validateSchema(data, validationSchema)

        automation = cls._assign_brokerage_account(bot_uuid, data, app)
        res = {}
        res['success'] = True
        res['automation'] = BotSchema(automation)
        return res

    @classmethod
    def _get_automation(cls, botUUID, app):
        db = app.current_request.db
        try:
            bot = db.query(TradingBot).filter(TradingBot.uuid == botUUID).one()
        except NoResultFound:
            raise APIException(f"No automation found with id: {botUUID}")
        return bot


    @classmethod
    def _get(cls, account_uuid, app):
        db = app.current_request.db
        try:
            account = AccountSerializer._get(account_uuid, app)
        except NoResultFound:
            raise APIException(f"No account found with id: {account_uuid}")
        bots = db.query(TradingBot).filter(TradingBot.account_id == account.id, TradingBot.status_id != 8).all()
        return bots


    @classmethod
    def get(cls, account_uuid, app):
        bots = cls._get(account_uuid, app)
        res = {}
        res['success'] = True
        res['automations'] = [BotSchema(bot) for bot in bots]
        return res


    @classmethod
    def _get_balance(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            auto = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        if auto.bot_type_id == 1:
            total_balance, available_cash = ETradeSerializer.balance(auto, app)
        elif auto.bot_type_id == 2:
            total_balance, available_cash = ""
        elif auto.bot_type_id == 3:
            total_balance, available_cash = RobinhoodSerializer.balance(auto, app)
        elif auto.bot_type_id == 4:
            total_balance, available_cash = ""
        elif auto.bot_type_id == 5:
            raise APIException("SignalStack does not return balances to Thriving automations")
        elif auto.bot_type_id == 6:
            updated, total_balance, available_cash = AlpacaSerializer.balance(auto, app)
        return auto, total_balance, available_cash


    @classmethod
    def get_balance(cls, bot_uuid, app):
        automation, total_balance, available_cash = cls._get_balance(bot_uuid, app)
        res = {}
        res['success'] = True
        automation_data = BotSchema(automation)
        # Add balance fields to the response
        automation_data['total_balance'] = float(total_balance) if total_balance else 0
        automation_data['available_cash'] = float(available_cash) if available_cash else 0
        res['automation'] = automation_data
        return res


    @classmethod
    def _update_balance(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            auto = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        if auto.bot_type_id == 1:
            updated, total_balance, available_cash = ETradeSerializer.update_balance(auto, app)
        elif auto.bot_type_id == 2:
            updated, total_balance, available_cash = SchwabSerializer.update_balance(auto, app)
        elif auto.bot_type_id == 3:
            updated, total_balance, available_cash = RobinhoodSerializer.account(auto, app)
        elif auto.bot_type_id == 4:
            updated, total_balance, available_cash = ""
        elif auto.bot_type_id == 5:
            raise APIException("SignalStack does not return brokerage balances. Cannot update the automation balance.")
        elif auto.bot_type_id == 6:
            updated, total_balance, available_cash = AlpacaSerializer.balance(auto, app)
        return updated, total_balance, available_cash


    @classmethod
    def update_balance(cls, bot_uuid, app):
        automation, total_balance, available_cash = cls._update_balance(bot_uuid, app)
        res = {}
        res['success'] = True
        res['automations'] = BotSchema(automation)
        return True


    @classmethod
    def _calculate_profit(cls, trade, app):
        db = app.current_request.db
        # print(colored(f"Sell order: {trade.id} - {trade.symbol} -- {trade.action_type_id}", "green"))

        ## Get all existing profit entries
        if trade.action_type_id in [2,8,9] and trade.status_id == 5:
            try:
                profit_entry_check = db.query(TradeResult).filter(TradeResult.trade_id_sell == trade.id).one()
                return True
            except NoResultFound:
                pass
            except MultipleResultsFound:
                raise APIException(f"Multiple profit entries made for trade id: {trade.uuid}")
        else:
            return True
        ## Get all trades previous - both buy and sell
        previous_trades = db.query(Trades).filter(Trades.symbol == trade.symbol, Trades.status_id == 5, Trades.trading_bot_id == trade.trading_bot_id, Trades.order_time < trade.order_time).order_by(Trades.order_time.desc()).all()

        ## Loop through them until they run into the next sell action
        cost = 0

        for p in previous_trades:
            if p.action_type_id in [1,5]:
                cost = cost + (p.price * p.quantity)
                # print(colored(f"Buy order: {p.id}", "yellow"))
            else:
                break

        ## Calculate the profit
        sell_price = float(trade.price)
        sell_order_value = float(trade.quantity) * sell_price
        profit = float(sell_order_value - cost)
        # print(colored(f"Order profit: {profit}", "magenta"))

        ## Create the DB entry
        newProfitEntry = TradeResult(
            trading_bot_id = trade.trading_bot_id,
            account_id = trade.account_id,
            symbol = trade.symbol,
            trade_id_sell = trade.id,
            sell_date = trade.order_time,
            profit = profit
        )
        try:
            db.add(newProfitEntry)
            db.commit()
        except:
            print(colored(f"order id could not calculate profit: {trade.id}", "red"))
            print(traceback.format_exc())
            db.rollback()
            raise APIException("Unable to save profit entry to database.")
        return profit


    @classmethod
    def _update_order_status(cls, trade_uuid, app):
        db = app.current_request.db
        try:
            trade = db.query(Trades).filter(Trades.uuid == trade_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate trade with id: {trade_uuid}")
        except MultipleResultsFound:
            raise APIException(f"Multiple orders found with order id: {trade_uuid}")

        if trade.automation.bot_type_id == 1:
            updated = ETradeSerializer.update_order_status(trade, app)
        elif trade.automation.bot_type_id == 2:
            updated = SchwabSerializer.update_order_status(trade, app)
        elif trade.automation.bot_type_id == 3:
            updated = RobinhoodSerializer.update_order_status(trade, app)
        elif trade.automation.bot_type_id == 4:
            pass
        elif trade.automation.bot_type_id == 5:
            raise APIException("SignalStack does not return brokerage balances. Cannot update the automation balance.")
        elif trade.automation.bot_type_id == 6:
            updated = AlpacaSerializer.update_order_status(trade, app)

        if trade.action_type_id in [2,8,9] and trade.status_id == 5:
            try:
                cls._calculate_profit(trade, app)
            except Exception as e:
                print(traceback.format_exc())
                print(f"Unable to calculate profit on order: {trade.uuid}")
        return trade


    @classmethod
    def update_order_status(cls, trade_uuid, app):
        trade = cls._update_order_status(trade_uuid, app)
        res = {}
        res['success'] = True
        res['trade'] = TradeSchema(trade)
        return res


class AutomationSerializer:
    @classmethod
    def monitor_stop_losses(cls, app):
        """
        Monitor all active positions and trigger sell orders for positions that have exceeded their stop loss thresholds.
        This function runs every 30 minutes to proactively check positions against their stop loss values.

        Returns:
            dict: Summary of monitoring results and any sell orders triggered
        """
        from chalicelib.Portfolios.models import Portfolio
        from chalicelib.Accounts.models import Account
        from chalicelib.Bots.models import TradingBot, Trades
        from chalicelib.Utils.functions import unixTimeNow
        from chalicelib.Logs.serializer import EventSerializer
        import requests
        import os
        import traceback

        db = app.current_request.db
        results = {
            'success': True,
            'positions_checked': 0,
            'stop_losses_triggered': 0,
            'errors': [],
            'triggered_positions': []
        }

        try:
            print("🛑 Starting stop loss monitoring...")

            # Get all active positions with stop loss values set
            active_positions = db.query(Portfolio).filter(
                Portfolio.active == True,
                Portfolio.quantity > 0,
                Portfolio.stop_loss.isnot(None),
                Portfolio.stop_loss > 0
            ).all()

            print(f"📊 Found {len(active_positions)} active positions with stop losses to monitor")

            for position in active_positions:
                try:
                    results['positions_checked'] += 1

                    # Get current price for the symbol
                    current_price = cls._get_current_price(position.symbol)
                    if not current_price:
                        results['errors'].append(f"Unable to fetch current price for {position.symbol}")
                        continue

                    print(f"📈 {position.symbol}: Current=${current_price:.2f}, Stop Loss=${float(position.stop_loss):.2f}, Quantity={float(position.quantity)}")

                    # Check if current price has fallen below stop loss threshold
                    if current_price <= float(position.stop_loss):
                        print(f"🚨 STOP LOSS TRIGGERED for {position.symbol}!")
                        print(f"   Current Price: ${current_price:.2f}")
                        print(f"   Stop Loss: ${float(position.stop_loss):.2f}")
                        print(f"   Loss Amount: ${(float(position.stop_loss) - current_price) * float(position.quantity):.2f}")

                        # Get the trading bot for this account
                        trading_bot = db.query(TradingBot).filter(
                            TradingBot.account_id == position.account_id,
                            TradingBot.status_id == 1,  # Active
                            TradingBot.brokerage_connected == 1  # Connected
                        ).first()

                        if not trading_bot:
                            error_msg = f"No active trading bot found for account {position.account_id}"
                            results['errors'].append(error_msg)
                            print(f"❌ {error_msg}")
                            continue

                        # Prepare sell order data
                        sell_data = {
                            'bot_uuid': trading_bot.uuid,
                            'symbol': position.symbol,
                            'action': 'sell',
                            'quantity': float(position.quantity),
                            'reason': 'stop_loss_monitor'
                        }

                        # Execute the sell order
                        try:
                            sell_result = cls.sell(sell_data, app)

                            if sell_result.get('success'):
                                results['stop_losses_triggered'] += 1
                                results['triggered_positions'].append({
                                    'symbol': position.symbol,
                                    'quantity': float(position.quantity),
                                    'current_price': current_price,
                                    'stop_loss': float(position.stop_loss),
                                    'account_id': position.account_id
                                })

                                # Log the event
                                EventSerializer.create(
                                    position.account_id,
                                    2,  # Warning level
                                    f"Stop loss triggered for {position.symbol}: Sold {position.quantity} shares at ${current_price:.2f} (Stop loss: ${float(position.stop_loss):.2f})",
                                    app,
                                    trading_bot.uuid
                                )

                                print(f"✅ Stop loss sell order placed for {position.symbol}")

                            else:
                                error_msg = f"Failed to place stop loss sell order for {position.symbol}: {sell_result.get('message', 'Unknown error')}"
                                results['errors'].append(error_msg)
                                print(f"❌ {error_msg}")

                        except Exception as sell_error:
                            error_msg = f"Exception placing stop loss sell order for {position.symbol}: {str(sell_error)}"
                            results['errors'].append(error_msg)
                            print(f"❌ {error_msg}")
                            print(traceback.format_exc())

                    else:
                        # Position is still above stop loss
                        loss_percentage = ((float(position.buy_price) - current_price) / float(position.buy_price)) * 100 if position.buy_price else 0
                        print(f"✅ {position.symbol} above stop loss (Current loss: {loss_percentage:.1f}%)")

                except Exception as position_error:
                    error_msg = f"Error processing position {position.symbol}: {str(position_error)}"
                    results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
                    print(traceback.format_exc())
                    continue

            print(f"🏁 Stop loss monitoring complete:")
            print(f"   Positions checked: {results['positions_checked']}")
            print(f"   Stop losses triggered: {results['stop_losses_triggered']}")
            print(f"   Errors: {len(results['errors'])}")

            return results

        except Exception as e:
            print(f"❌ Critical error in stop loss monitoring: {str(e)}")
            print(traceback.format_exc())
            results['success'] = False
            results['errors'].append(f"Critical error: {str(e)}")
            return results

    @classmethod
    def _get_current_price(cls, symbol):
        """
        Get current price for a symbol using the AI API.

        Args:
            symbol (str): Stock symbol

        Returns:
            float: Current price or None if unable to fetch
        """
        import requests
        import os

        try:
            url = f"{os.environ['AI_URL']}/symbol/{symbol}/live-quote"
            headers = {
                "x-api-key": os.environ['AI_API_KEY']
            }

            response = requests.get(url=url, headers=headers, timeout=10)
            response.raise_for_status()

            data = response.json()

            # Handle different response formats
            if 'live_quote' in data and 'price' in data['live_quote']:
                return float(data['live_quote']['price'])
            elif 'quote' in data and 'price' in data['quote']:
                return float(data['quote']['price'])
            elif 'price' in data:
                return float(data['price'])
            else:
                print(f"⚠️ Unexpected price response format for {symbol}: {data}")
                return None

        except requests.exceptions.Timeout:
            print(f"⚠️ Timeout fetching price for {symbol}")
            return None
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Request error fetching price for {symbol}: {str(e)}")
            return None
        except (KeyError, ValueError, TypeError) as e:
            print(f"⚠️ Data parsing error for {symbol}: {str(e)}")
            return None
        except Exception as e:
            print(f"⚠️ Unexpected error fetching price for {symbol}: {str(e)}")
            return None

    @classmethod
    def _buy(cls, data, app, DT_LIMIT=False):
        db = app.current_request.db
        tbot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()

        ## Etrade
        if tbot.bot_type_id == 1:
            trade = ETradeSerializer.buy(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## Schwab
        elif tbot.bot_type_id == 2:
            trade = SchwabSerializer.buy(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## Robinhood
        elif tbot.bot_type_id == 3:
            trade = RobinhoodSerializer.buy(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## TradeStation
        elif tbot.bot_type_id == 4:
            trade = None

        ## SignalStack
        elif tbot.bot_type_id == 5:
            trade = SignalStackSerializer.buy(data, tbot, app, DT_CHECK=tbot.day_trader)

        ## Alpaca
        elif tbot.bot_type_id == 6:
            trade = AlpacaSerializer.buy(data, tbot, app, DT_CHECK=tbot.day_trader)

        ## Log the event
        if trade.price == None:
            price = "market price"
        else:
            price = trade.price
        EventSerializer.create(trade.account_id, 4, f"Automation buy order placed for {data['symbol']} at the price: {price}.", app, BOT_UUID=data['bot_uuid'], TRADE_UUID=trade.uuid)
        return trade


    @classmethod
    def buy(cls, data, app, DT_LIMIT=False):
        # Validate incoming request
        validationSchema = readJsonFile('/Bots/RequestJson/order.json')
        validateSchema(data, validationSchema)
        db = app.current_request.db

        # 🔍 MARKET SENTIMENT CHECK - Block ALL buy orders when sentiment is very negative
        try:
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            sentiment_check = MarketSentimentSerializer.should_allow_trading("buy")
            sentiment_level = sentiment_check.get("sentiment_level", "Unknown")

            # Block buy orders if market sentiment is very negative
            if not sentiment_check["allow_trading"] or sentiment_level in ["Very Negative"]:
                print(f"🚫 SYSTEM-WIDE BUY ORDER BLOCKED: Market sentiment is {sentiment_level} (very negative) - {sentiment_check['reason']}")
                from chalicelib.Events.serializer import EventSerializer

                # Log the blocked order event
                try:
                    bot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).first()
                    if bot:
                        EventSerializer.create(
                            bot.account_id,
                            2,  # Warning event
                            f"Buy order for {data['symbol']} blocked due to very negative market sentiment: {sentiment_level} ({sentiment_check.get('sentiment_score', 0):.3f})",
                            app,
                            BOT_UUID=data['bot_uuid']
                        )
                except Exception as e:
                    print(f"⚠️ Could not log sentiment block event: {e}")

                raise APIException(f"Buy order blocked due to market sentiment: {sentiment_level} (very negative threshold). All buy orders are suspended when market sentiment is very negative. Reason: {sentiment_check['reason']}")
            else:
                print(f"✅ SYSTEM-WIDE BUY ORDER APPROVED: Market sentiment is {sentiment_level} - {sentiment_check['reason']}")

        except ImportError:
            print("⚠️ Market sentiment system not available - proceeding with buy order")
        except APIException:
            # Re-raise APIException (our blocked buy order)
            raise
        except Exception as e:
            print(f"⚠️ Error checking market sentiment: {e} - proceeding with buy order")

        market_open = cls.market_status()

        ## Get current market status
        headers = {
            "x-api-key": os.environ['AI_API_KEY']
        }
        url = f"{os.environ['AI_URL']}/symbol/{data['symbol']}/ohlc-live/1min"
        # url = f"{os.environ['AI_URL']}/symbol/{data['symbol']}/live-quote"

        ## Get current price again
        if market_open == True:
            try:
                res = requests.get(url=url, headers=headers)
                live_quote = res.json()
                data['closing_price'] = live_quote['symbol'][0]['close']
                data['price'] = None
            except Exception as e:
                print(e)
                print(traceback.format_exc())

        else:
            try:
                res = requests.get(url=url, headers=headers)
                live_quote = res.json()
                data['closing_price'] = live_quote['symbol'][0]['close']
                # data['price'] = live_quote['live_quote']['price']
                data['price'] = live_quote['symbol'][0]['close']
            except Exception as e:
                print(e)
                print(traceback.format_exc())


        ## Issue the order
        trade = cls._buy(data, app)

        ## Get the symbol from the portfolio
        try:
            portfolio_symbol = db.query(Portfolio).filter(Portfolio.symbol == data['symbol'], Portfolio.account_id == data['account_id'], Portfolio.status_id != 8).one()
            ## Set active flags on the symbol
            if trade.action_type_id in [1,5]:
                portfolio_symbol.active = True
                portfolio_symbol.cooldown = False

                try:
                    db.commit()
                except:
                    print(traceback.format_exc())
                    db.rollback()
                    raise APIException(f"Unable to update buy order status on trade: {trade.uuid}")
        except NoResultFound:
            pass

        ## Get the account balance to check if account can do a immediate stop loss order
        balance = db.query(AccountBalance).filter(AccountBalance.account_id == data['account_id']).order_by(AccountBalance.created_at.desc()).first()
        try:
            bot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()
            account = bot.account
            if balance and float(balance.balance) > 25000 and market_open == True and account.auto_stop_loss == True:
                data['closing_price'] = live_quote['symbol'][0]['close']
                print("Trailing order created")
                cls.stop_loss(data, app)
        except NoResultFound:
            print(f"no stop loss created: {float(balance.balance)} - {market_open}")

        response = {}
        response['success'] = True
        response['trade'] = TradeSchema(trade)
        return response


    @classmethod
    def _sell(cls, data, app, DT_LIMIT=False):
        db = app.current_request.db
        tbot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()

        ## Etrade
        if tbot.bot_type_id == 1:
            trade = ETradeSerializer.sell(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## Schwab
        elif tbot.bot_type_id == 2:
            trade = SchwabSerializer.limit_sell(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## Robinhood
        elif tbot.bot_type_id == 3:
            trade = RobinhoodSerializer.sell_market(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## TradeStation
        elif tbot.bot_type_id == 4:
            trade = RobinhoodSerializer.limit_sell(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## SignalStack
        elif tbot.bot_type_id == 5:
            trade = SignalStackSerializer._sell(data, tbot, app, DT_CHECK=tbot.day_trader, DT_LIMIT=tbot.day_trader)

        ## Alpaca
        elif tbot.bot_type_id == 6:
            trade = AlpacaSerializer.sell(data, tbot, app, DT_CHECK=tbot.day_trader)

        ## Log the event
        if trade.price == None:
            price = "market price"
        else:
            price = trade.price

        ## Log the event
        EventSerializer.create(trade.account_id, 4, f"Automation sell order placed for {data['symbol']} at the price: {price}.", app, BOT_UUID=data['bot_uuid'], TRADE_UUID=trade.uuid)
        return trade


    @classmethod
    def sell(cls, data, app, DT_LIMIT=False):
        db = app.current_request.db
        # Validate incoming request
        validationSchema = readJsonFile('/Bots/RequestJson/order.json')
        validateSchema(data, validationSchema)

        # Get the bot to extract account_id if not provided
        if 'account_id' not in data:
            try:
                tbot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()
                data['account_id'] = tbot.account_id
            except NoResultFound:
                raise APIException(f"Unable to locate automation with id: {data['bot_uuid']}")

        # 🔍 MARKET SENTIMENT CHECK - Log sentiment context for sell orders
        try:
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            sentiment_check = MarketSentimentSerializer.should_allow_trading("sell")

            print(f"📊 SELL ORDER SENTIMENT: {sentiment_check['reason']}")

            # If sentiment is very negative, this is a good sell decision
            if sentiment_check["sentiment_level"] in ["Very Negative", "Negative"]:
                print(f"🔴 SENTIMENT-DRIVEN SELL: Market conditions support selling {data['symbol']}")

                # Log the sentiment-supported sell event
                try:
                    bot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).first()
                    if bot:
                        from chalicelib.Events.serializer import EventSerializer
                        EventSerializer.create(
                            bot.account_id,
                            4,  # Trade event
                            f"Sell order for {data['symbol']} supported by negative market sentiment: {sentiment_check['sentiment_level']} ({sentiment_check['sentiment_score']:.3f})",
                            app,
                            BOT_UUID=data['bot_uuid']
                        )
                except Exception as e:
                    print(f"⚠️ Could not log sentiment sell event: {e}")

        except ImportError:
            print("⚠️ Market sentiment system not available - proceeding with sell order")
        except Exception as e:
            print(f"⚠️ Error checking market sentiment: {e} - proceeding with sell order")

        market_open = cls.market_status()

        ## Get current price again
        # url = f"{os.environ['AI_URL']}/symbol/{data['symbol']}/live-quote"
        url = f"{os.environ['AI_URL']}/symbol/{data['symbol']}/ohlc-live/1min"
        headers = {
            "x-api-key": os.environ['AI_API_KEY']
        }
        if market_open == True:
            try:
                res = requests.get(url=url, headers=headers)
                live_quote = res.json()
                data['closing_price'] = live_quote['symbol'][0]['close']
                # data['closing_price'] = live_quote['live_quote']['price']
                data['price'] = None
            except Exception as e:
                print(e)
                print(traceback.format_exc())

        else:
            try:
                res = requests.get(url=url, headers=headers)
                live_quote = res.json()
                # data['closing_price'] = live_quote['live_quote']['price']
                data['closing_price'] = live_quote['symbol'][0]['close']
                # data['price'] = live_quote['live_quote']['price']
                data['price'] = live_quote['symbol'][0]['close']
            except Exception as e:
                print(e)
                print(traceback.format_exc())

        ## Issue the order
        trade = cls._sell(data, app, DT_LIMIT=DT_LIMIT)

        if trade == False:
            raise APIException("Unable to place sell order")

        ## Get the symbol from the portfolio
        db = app.current_request.db
        try:
            portfolio_symbol = db.query(Portfolio).filter(Portfolio.symbol == data['symbol'], Portfolio.account_id == data['account_id'], Portfolio.status_id != 8).one()
            ## Set active flags on the symbol
            if trade.action_type_id in [2,8]:
                portfolio_symbol.active = False
                try:
                    tbot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()
                    if tbot.day_trader:
                        portfolio_symbol.cooldown = False
                    else:
                        portfolio_symbol.cooldown = True
                except NoResultFound:
                    raise APIException(f"Unable to update portfolio status after sell order for {data['symbol']}.")
                try:
                    db.commit()
                except:
                    print(traceback.format_exc())
                    db.rollback()
                    raise APIException(f"Unable to update sell order status on trade: {trade.uuid}")
        except NoResultFound:
            print(f"⚠️ No portfolio record found for symbol={data['symbol']}, account_id={data['account_id']} - this is normal for some cases")
            pass
        except Exception as e:
            print(f"❌ Unexpected error updating portfolio for {data['symbol']}: {e}")
            print(traceback.format_exc())
            # Don't raise the exception - let the sell order succeed even if portfolio update fails
            pass

        # Update positions after sell order
        print(f"🔄 Updating positions after sell order for {data['symbol']}")
        cls.update_positions(data['bot_uuid'], app)

        response = {}
        response['success'] = True
        response['trade'] = TradeSchema(trade)
        response['symbol_sold'] = data['symbol']  # Add symbol info for frontend
        print(f"✅ Sell order completed successfully for {data['symbol']}")
        return response


    @classmethod
    def cancel_order(cls, tradeUUID, app):
        # Validate incoming request
        db = app.current_request.db
        try:
            trade = db.query(Trades).filter(Trades.uuid == tradeUUID).one()
            trade = cls._cancel_order(trade, app)
        except NoResultFound:
            raise APIException(f"Unable to locate trade with id: {tradeUUID}")
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            raise APIException(f"Unknown problem when canceling trade with id: {tradeUUID}")

        response = {}
        response['success'] = True
        response['trade'] = (TradeSchema(trade) if trade else None)
        return response


    @classmethod
    def cancel_all_orders(cls, automationUUID, app):
        # Validate incoming request
        db = app.current_request.db
        try:
            automation = db.query(TradingBot).filter(TradingBot.uuid == automationUUID).one()
            trades = cls._cancel_all_orders(automation, app)
        except NoResultFound:
            raise APIException(f"Unable to cancel all open orders")
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            raise APIException(f"Unknown problem when canceling all open orders.")

        response = {}
        response['success'] = True
        response['trade'] = [TradeSchema(x) for x in trades]
        return response


    @classmethod
    def _cancel_all_orders(cls, automation, app):
        db = app.current_request.db
        res = None
        ## Etrade
        if automation.bot_type_id == 1:
            res = ETradeSerializer.cancel_order(automation, app)

        ## Schwab
        elif automation.bot_type_id == 2:
            res = SchwabSerializer.cancel_order(automation, app)

        ## Robinhood
        elif automation.bot_type_id == 3:
            res = RobinhoodSerializer.cancel_order(automation, app)

        ## Tradestation
        elif automation.bot_type_id == 4:
            res = RobinhoodSerializer.cancel_order(automation, app)

        ## SignalStack
        elif automation.bot_type_id == 5:
            res = RobinhoodSerializer.cancel_order(automation, app)

        ## Alpaca
        elif automation.bot_type_id == 6:
            res = AlpacaSerializer.cancel_all_orders(automation, app)

        ## Log the event
        if res:
            EventSerializer.create(automation.account_id, 4, f"Automation cancelled all open orders.", app, BOT_UUID=automation.uuid)
            return res
        else:
            raise APIException(f"Unable to cancel orders for automation: {automation.uuid}")


    @classmethod
    def _cancel_order(cls, trade, app):
        db = app.current_request.db
        bot = db.query(TradingBot).join(Trades, Trades.trading_bot_id == TradingBot.id).filter(Trades.id == trade.id).one()
        res = None
        ## Etrade
        if bot.bot_type_id == 1:
            res = ETradeSerializer.cancel_order(bot, trade, app)

        ## Schwab
        elif bot.bot_type_id == 2:
            res = SchwabSerializer.cancel_order(bot, trade, app)

        ## Robinhood
        elif bot.bot_type_id == 3:
            res = RobinhoodSerializer.cancel_order(bot, trade.id, app)

        ## Tradestation
        elif bot.bot_type_id == 4:
            res = RobinhoodSerializer.cancel_order(bot, trade, app)

        ## SignalStack
        elif bot.bot_type_id == 5:
            res = RobinhoodSerializer.cancel_order(bot, trade, app)

        ## Alpaca
        elif bot.bot_type_id == 6:
            res = AlpacaSerializer.cancel_order(bot, trade.id, app)

        ## Log the event
        if res:
            EventSerializer.create(res.account_id, 4, f"Automation cancelled order for {res.symbol} with id: {res.uuid}.", app, BOT_UUID=bot.uuid, TRADE_UUID=trade.uuid)
            return res
        else:
            raise APIException(f"Unable to cancel order for trade id: {trade.uuid}")


    @classmethod
    def _stop_loss(cls, data, app, DT_LIMIT=False):
        db = app.current_request.db
        print("STOP LOSS", data)
        try:
            bot = db.query(TradingBot).join(Trades, Trades.trading_bot_id == TradingBot.id).filter(TradingBot.uuid == data['bot_uuid'], Trades.uuid == data['trade_uuid']).one()
        except KeyError as k:
            print(str(k))
            if str(k) == "'trade_uuid'":
                bot = db.query(TradingBot).join(Trades, Trades.trading_bot_id == TradingBot.id).filter(TradingBot.uuid == data['bot_uuid'], Trades.symbol == data['symbol'], Trades.status_id.in_([4,5])).order_by(Trades.order_time.desc()).first()
                if bot == None:
                    return None
        except NoResultFound:
            raise APIException(f"Unable to locate trade id: {data['trade_uuid']} within automation: {data['bot_uuid']}")
        res = None

        ## Etrade
        if bot.bot_type_id == 1:
            res = ETradeSerializer.stop_loss(data, bot, app, DT_CHECK=bot.day_trader, DT_LIMIT=bot.day_trader)

        ## Schwab
        elif bot.bot_type_id == 2:
            res = SchwabSerializer.stop_loss(data, bot, app, DT_CHECK=bot.day_trader, DT_LIMIT=bot.day_trader)

        ## Robinhood
        elif bot.bot_type_id == 3:
            res = RobinhoodSerializer.stop_loss(data, bot, app, DT_CHECK=bot.day_trader, DT_LIMIT=bot.day_trader)

        ## Tradestation
        elif bot.bot_type_id == 4:
            res = RobinhoodSerializer.stop_loss(data, bot, app)

        ## SignalStack
        elif bot.bot_type_id == 5:
            res = RobinhoodSerializer.stop_loss(data, bot, app)

        ## Alpaca
        elif bot.bot_type_id == 6:
            res = AlpacaSerializer.stop_loss(data, bot, app, DT_CHECK=bot.day_trader, DT_LIMIT=bot.day_trader)

        ## Log the event
        if res:
            EventSerializer.create(res.account_id, 4, f"Automation stop loss order created for {res.symbol} at {res.limit_stop}.", app, BOT_UUID=bot.uuid, TRADE_UUID=res.uuid)
            return res
        else:
            raise APIException(f"Unable to process stop loss for trade id: {data['trade_uuid']}")

    @classmethod
    def stop_loss(cls, data, app, DT_LIMIT=False):
        db = app.current_request.db
        # Validate incoming request
        validationSchema = readJsonFile('/Bots/RequestJson/order.json')
        validateSchema(data, validationSchema)

        market_open = cls.market_status()

        ## Get current price again
        url = f"{os.environ['AI_URL']}/symbol/{data['symbol']}/ohlc-live/1min"
        headers = {
            "x-api-key": os.environ['AI_API_KEY']
        }
        try:
            res = requests.get(url=url, headers=headers)
            live_quote = res.json()
            # data['closing_price'] = live_quote['live_quote']['price']
            data['closing_price'] = live_quote['symbol'][0]['close']
            data['price'] = live_quote['symbol'][0]['close']
        except Exception as e:
            print(e)
            print(traceback.format_exc())

        trade = cls._stop_loss(data, app, DT_LIMIT=DT_LIMIT)

        if trade == False:
            raise APIException("Unable to place stop loss order")

        ## Get the symbol from the portfolio
        db = app.current_request.db
        try:
            portfolio_symbol = db.query(Portfolio).filter(Portfolio.symbol == data['symbol'], Portfolio.account_id == data['account_id'], Portfolio.status_id != 8).one()
            ## Set active flags on the symbol
            if trade.action_type_id in [1,5,9]:
                portfolio_symbol.active = True
                portfolio_symbol.cooldown = False

                try:
                    db.commit()
                except:
                    print(traceback.format_exc())
                    db.rollback()
                    raise APIException(f"Unable to update stop loss order status on trade: {trade.uuid}")

        except NoResultFound:
            pass

        response = {}
        response['success'] = True
        response['trade'] = (TradeSchema(trade) if trade else None)
        return response


    @classmethod
    def _get_positions(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            bot = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        ## Etrade
        if bot.bot_type_id == 1:
            res = ETradeSerializer.current_positions(bot, app)

        ## Schwab
        elif bot.bot_type_id == 2:
            res = SchwabSerializer.positions(bot, app)

        ## Robinhood
        elif bot.bot_type_id == 3:
            res = RobinhoodSerializer.positions(bot, app)

        ## Tradestation
        elif bot.bot_type_id == 4:
            res = None

        ## SignalStack
        elif bot.bot_type_id == 5:
            raise APIException("Cannot track current positions held for Signal Stack users")

        ## Alpaca
        elif bot.bot_type_id == 6:
            res = AlpacaSerializer.positions(bot, app)

        return res


    @classmethod
    def get_positions(cls, bot_uuid, app):
        positions = cls._get_positions(bot_uuid, app)
        response = {}
        response['success'] = True
        response['positions'] = positions
        return response



    @classmethod
    def update_positions(cls, bot_uuid, app):
        db = app.current_request.db
        automations = db.query(TradingBot).filter(TradingBot.status_id == 1, TradingBot.uuid == bot_uuid).all()
        for a in automations:
            ## Get the active symbols
            active_list = []
            positions = AutomationSerializer._get_positions(a.uuid, app)
            print(f"📊 Current positions from broker for bot {bot_uuid}: {[p['symbol'] for p in positions]}")
            for p in positions:
                active_list.append(p['symbol'])

            print(f"📋 Active symbols list: {active_list}")

            inactive_symbols = db.query(Portfolio).filter(Portfolio.account_id == a.account_id, Portfolio.active == True, Portfolio.symbol.not_in(active_list)).all()
            print(f"🔴 Setting inactive (sold): {[i.symbol for i in inactive_symbols]}")
            for i in inactive_symbols:
                print(f"   - Setting {i.symbol} to inactive")
                i.active = False
                i.quantity = 0
                i.buy_price = None
                i.percent_change = None
                i.amount_change = None
                i.gain = None
                if a.day_trader:
                    i.cooldown = False
                else:
                    i.cooldown = True

            active_symbols = db.query(Portfolio).filter(Portfolio.account_id == a.account_id, Portfolio.symbol.in_(active_list)).all()
            for portfolio_item in active_symbols:
                # print(colored(portfolio_item.symbol, "green"))
                portfolio_item.active = True
                portfolio_item.cooldown = False

                # Find the corresponding position data from broker
                position_data = next((p for p in positions if p['symbol'] == portfolio_item.symbol), None)
                if position_data:
                    # Update quantity and buy price from broker data
                    portfolio_item.quantity = position_data['quantity']
                    portfolio_item.buy_price = position_data['buy_price']

                    # Calculate and set proper stop loss price (respecting 1.5% max limit)
                    buy_price = float(position_data['buy_price'])
                    # Use 1.5% max stop loss as per user requirement
                    max_allowed_stop_loss = buy_price * 0.985  # Maximum 1.5% loss
                    portfolio_item.stop_loss = max_allowed_stop_loss

                    print(f"📊 Updated {portfolio_item.symbol}: qty={portfolio_item.quantity}, buy_price=${buy_price:.2f}, stop_loss=${max_allowed_stop_loss:.2f}")

            try:
                db.commit()

                ## Log the event
                EventSerializer.create(a.account_id, 7, f"Automation {bot_uuid} updated current positions.", app, BOT_UUID=bot_uuid)
            except Exception as e:
                db.rollback()
                print(e)
                db.close()
                print(traceback.format_exc())
                raise APIException("Unable to save updated positions")
        res = {}
        res['success'] = True
        return res

    # Constants for better maintainability
    STATUS_PENDING = 4
    STATUS_FILLED = 5
    STATUS_CANCELLED = 16

    BUY_ACTIONS = [1, 3, 5]  # market_buy, hold, limit_buy
    SELL_ACTIONS = [2, 4, 8]  # market_sell, wait, limit_sell
    STOP_LOSS_ACTION = 9

    DUPLICATE_PREVENTION_MINUTES = 5
    MAX_POSITION_PERCENTAGE = 10  # Max 10% of portfolio in single stock

    @classmethod
    def signal(cls, bot, analysis, app):
        from chalicelib.Portfolios.models import Portfolio, SymbolAction
        from chalicelib.Bots.models import TradingBot, Trades
        from chalicelib.Bots.serializer import BotSerializer, AutomationSerializer
        from sqlalchemy.orm.exc import NoResultFound
        import datetime
        import time
        import logging
        from pytz import timezone

        # Performance monitoring
        start_time = time.time()
        logger = logging.getLogger(__name__)

        db = app.current_request.db

        try:
            # Validate analysis data
            validation_result = cls._validate_analysis_data(analysis)
            if not validation_result['success']:
                return validation_result

            market_open = cls.market_status()

            ## Get portfolio item
            try:
                portfolio_item = db.query(Portfolio).filter(
                    Portfolio.account_id == bot.account_id,
                    Portfolio.symbol == analysis['symbol'],
                    Portfolio.status_id == 1
                ).one()
            except NoResultFound:
                return {
                    'success': False,
                    'message': f"Account {bot.account_id} does not have symbol {analysis['symbol']} active."
                }

            ### Check if stop loss is enabled
            if analysis['market_session'] == 'stop_loss':
                account = portfolio_item.account
                if not account.auto_stop_loss:
                    return {
                        'success': False,
                        'message': f"Account {bot.account_id} has stop loss orders disabled."
                    }

            ## Get the last trade for symbol on each automation
            try:
                trades = db.query(Trades).filter(
                    Trades.trading_bot_id == bot.id,
                    Trades.symbol == analysis['symbol'],
                    Trades.status_id.in_([cls.STATUS_PENDING, cls.STATUS_FILLED])
                ).order_by(Trades.order_time.desc()).first()
            except Exception as e:
                logger.error(f"Error querying trades for bot {bot.uuid}, symbol {analysis['symbol']}: {e}")
                return {
                    'success': False,
                    'message': "Error retrieving trade history"
                }

            # Check for recent duplicate orders
            duplicate_check = cls._check_for_duplicate_orders(db, bot, analysis)
            if not duplicate_check['success']:
                return duplicate_check

            ## Get balances with proper error handling
            try:
                BotSerializer._update_balance(bot.uuid, app)
                auto, total_balance, available_cash = BotSerializer._get_balance(bot.uuid, app)

                # Validate balance data
                if total_balance is None or available_cash is None:
                    raise ValueError("Invalid balance data received")

            except Exception as e:
                logger.error(f"Failed to get balance for bot {bot.uuid}: {e}")
                return {
                    'success': False,
                    'message': "Unable to retrieve account balance"
                }

            ## Get open positions with fallback to database
            position = None
            try:
                positions = cls._get_positions(bot.uuid, app)
                for p in positions:
                    if p['symbol'] == analysis['symbol']:
                        position = p
                        break
            except Exception as e:
                logger.warning(f"API position retrieval failed for bot {bot.uuid}: {e}. Falling back to database query.")

                # Fallback: Query database for active positions
                try:
                    from chalicelib.Bots.models import Trades

                    # Get the most recent filled buy trade for this symbol
                    last_buy_trade = db.query(Trades).filter(
                        Trades.trading_bot_id == bot.id,
                        Trades.symbol == analysis['symbol'],
                        Trades.action_type_id.in_(cls.BUY_ACTIONS),
                        Trades.status_id == cls.STATUS_FILLED
                    ).order_by(Trades.order_time.desc()).first()

                    # Get the most recent filled sell trade for this symbol
                    last_sell_trade = db.query(Trades).filter(
                        Trades.trading_bot_id == bot.id,
                        Trades.symbol == analysis['symbol'],
                        Trades.action_type_id.in_(cls.SELL_ACTIONS),
                        Trades.status_id == cls.STATUS_FILLED
                    ).order_by(Trades.order_time.desc()).first()

                    # Determine if we have an active position based on trade history
                    if last_buy_trade:
                        # If no sell trade, or buy trade is more recent than sell trade
                        if not last_sell_trade or last_buy_trade.order_time > last_sell_trade.order_time:
                            # Create a position object from database data
                            position = {
                                'symbol': analysis['symbol'],
                                'quantity': int(last_buy_trade.quantity),
                                'market_value': float(last_buy_trade.quantity) * float(analysis['closing_price']),
                                'avg_cost': float(last_buy_trade.price) if last_buy_trade.price else 0,
                                'source': 'database_fallback'
                            }
                            logger.info(f"Using database fallback position for {analysis['symbol']}: {position['quantity']} shares")
                        else:
                            logger.info(f"No active position found in database for {analysis['symbol']} (last sell more recent than last buy)")
                    else:
                        logger.info(f"No buy trades found in database for {analysis['symbol']}")

                except Exception as db_error:
                    logger.error(f"Database fallback also failed for bot {bot.uuid}: {db_error}")
                    return {
                        'success': False,
                        'message': "Error retrieving position data from both API and database"
                    }

            # Validate sell orders have positions
            if analysis['action_type_id'] in cls.SELL_ACTIONS + [cls.STOP_LOSS_ACTION] and position is None:
                return {
                    'success': False,
                    'message': f"No position found for {analysis['symbol']} - cannot execute sell order"
                }

            # Check for pending orders (consolidated duplicate prevention)
            if trades and trades.status_id == cls.STATUS_PENDING:
                if trades.action_type_id == analysis['action_type_id']:
                    logger.info(f"🚫 Duplicate order prevention: Pending {analysis['action']} order exists for {analysis['symbol']} on bot {bot.uuid}")
                    return {
                        'success': False,
                        'message': f"Pending {analysis['action']} order already exists for {analysis['symbol']}"
                    }

                # Prevent multiple buy orders
                if analysis['action_type_id'] in cls.BUY_ACTIONS and trades.action_type_id in cls.BUY_ACTIONS:
                    logger.info(f"🚫 Duplicate buy prevention: Pending buy order exists for {analysis['symbol']} on bot {bot.uuid}")
                    return {
                        'success': False,
                        'message': f"Pending buy order already exists for {analysis['symbol']}"
                    }

            # Calculate order parameters
            order_params = cls._calculate_order_parameters(
                analysis, portfolio_item, total_balance, available_cash, position, trades
            )

            if not order_params['success']:
                return order_params

            quantity = order_params['quantity']
            cost = order_params['cost']
            maxCash = order_params['maxCash']
            stop_loss = order_params['stop_loss']

            # Safety check for position size
            if analysis['action_type_id'] in cls.BUY_ACTIONS and total_balance > 0:
                position_percentage = (cost / total_balance) * 100
                if position_percentage > cls.MAX_POSITION_PERCENTAGE:
                    return {
                        'success': False,
                        'message': f"Position size ({position_percentage:.1f}%) exceeds safety limit ({cls.MAX_POSITION_PERCENTAGE}%)"
                    }

            # Build order object
            order = {
                "bot_uuid": bot.uuid,
                "symbol": analysis['symbol'],
                "account_id": bot.account_id,
                "max_cash": maxCash,
                "cost": cost,
                "action": analysis['action'],
                "quantity": quantity,
                "stop_loss": stop_loss,
                "analysis_action": analysis['action'],
                "price": float(analysis['closing_price'])
            }

            # Execute trading logic with proper transaction handling
            result = cls._execute_trading_logic(bot, analysis, trades, order, portfolio_item, position, app)

            # Log performance metrics
            execution_time = time.time() - start_time
            logger.info(f"Signal processing completed in {execution_time:.3f}s for {analysis['symbol']} - Result: {result.get('success', False)}")

            return result

        except Exception as e:
            logger.error(f"Signal processing error for bot {bot.uuid}, symbol {analysis['symbol']}: {e}")
            return {
                'success': False,
                'message': f"Signal processing failed: {str(e)}"
            }

    @classmethod
    def _validate_analysis_data(cls, analysis):
        """Validate incoming analysis data"""
        required_fields = ['symbol', 'action', 'action_type_id', 'closing_price', 'stop_loss']

        for field in required_fields:
            if field not in analysis:
                return {
                    'success': False,
                    'message': f"Missing required field: {field}"
                }

        # Validate numeric fields
        try:
            closing_price = float(analysis['closing_price'])
            stop_loss = float(analysis['stop_loss'])

            if closing_price <= 0:
                return {
                    'success': False,
                    'message': "Invalid closing price: must be positive"
                }

            if stop_loss < 0:
                return {
                    'success': False,
                    'message': "Invalid stop loss: cannot be negative"
                }

        except (ValueError, TypeError):
            return {
                'success': False,
                'message': "Invalid numeric data in analysis"
            }

        return {'success': True}

    @classmethod
    def _check_for_duplicate_orders(cls, db, bot, analysis):
        """Check for recent duplicate orders"""
        from datetime import datetime, timedelta

        duplicate_check_time = datetime.utcnow() - timedelta(minutes=cls.DUPLICATE_PREVENTION_MINUTES)

        recent_duplicate = db.query(Trades).filter(
            Trades.trading_bot_id == bot.id,
            Trades.symbol == analysis['symbol'],
            Trades.action_type_id == analysis['action_type_id'],
            Trades.order_time >= duplicate_check_time,
            Trades.status_id.in_([cls.STATUS_PENDING, cls.STATUS_FILLED])
        ).first()

        if recent_duplicate:
            return {
                'success': False,
                'message': f"Recent {analysis['action']} order for {analysis['symbol']} already placed within last {cls.DUPLICATE_PREVENTION_MINUTES} minutes"
            }

        return {'success': True}

    @classmethod
    def _calculate_order_parameters(cls, analysis, portfolio_item, total_balance, available_cash, position, trades):
        """Calculate order quantity, cost, and other parameters"""
        try:
            stop_loss = float(analysis['stop_loss'])
            closing_price = float(analysis['closing_price'])
            quantity = 0
            cost = 0
            maxCash = 0

            if analysis['action_type_id'] in cls.BUY_ACTIONS:
                # Determine max cash for purchases
                # Handle None ratio values with a default of 20%
                portfolio_ratio = portfolio_item.ratio if portfolio_item.ratio is not None else 20.0
                maxCash = round(float(total_balance) * float(portfolio_ratio) / 100, 2)

                # Adjust for existing positions (use actual position data when available)
                if trades and position:
                    # Use actual position value instead of estimated calculation
                    current_position_value = position.get('market_value', 0)
                    maxCash = max(0, maxCash - current_position_value)

                if maxCash > 0 and closing_price > 0:
                    quantity = int(maxCash / closing_price)
                    cost = float(quantity) * closing_price

                    # Fix the quantity calculation bug - was backwards before
                    if cost > available_cash:
                        quantity = int(available_cash / closing_price)
                        cost = float(quantity) * closing_price

                    # Ensure we don't exceed available cash
                    if cost > available_cash:
                        quantity = 0
                        cost = 0

            elif analysis['action_type_id'] in cls.SELL_ACTIONS + [cls.STOP_LOSS_ACTION] and position:
                quantity = position['quantity']

            # Override with explicit quantity if provided
            if analysis.get('quantity'):
                quantity = int(analysis['quantity'])
                cost = float(quantity) * closing_price

            return {
                'success': True,
                'quantity': quantity,
                'cost': cost,
                'maxCash': maxCash,
                'stop_loss': stop_loss
            }

        except Exception as e:
            return {
                'success': False,
                'message': f"Error calculating order parameters: {str(e)}"
            }

    @classmethod
    def _execute_trading_logic(cls, bot, analysis, trades, order, portfolio_item, position, app):
        """Execute the main trading logic with proper transaction handling"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # If there are no existing orders for this symbol, only buy orders can execute
            if trades is None:
                return cls._handle_no_existing_trades(bot, analysis, order, app)
            else:
                # Handle existing trades scenario
                last_action_id = trades.action_type_id
                order['last_action_id'] = last_action_id
                order['last_trade_status'] = trades.status_id

                return cls._handle_existing_trades(bot, analysis, trades, order, portfolio_item, position, app)

        except Exception as e:
            logger.error(f"Trading logic execution error: {e}")
            return {
                'success': False,
                'message': f"Trading execution failed: {str(e)}"
            }

    @classmethod
    def _handle_no_existing_trades(cls, bot, analysis, order, app):
        """Handle trading when no existing trades exist for the symbol"""
        if cls._can_execute_order(bot, analysis):
            if analysis['action'] == "buy" and order['quantity'] > 0:
                print(f"✅ Buy - no existing orders for {analysis['symbol']}")
                return cls.buy(order, app, DT_LIMIT=False)
            elif analysis['action'] == "hold" and order['quantity'] > 0:
                print(f"✅ Buy (hold) - no existing orders for {analysis['symbol']}")
                return cls.buy(order, app, DT_LIMIT=False)

        return {'success': True, 'message': 'No action taken - conditions not met'}

    @classmethod
    def _can_execute_order(cls, bot, analysis):
        """Check if order can be executed based on day trader status and market session"""
        return (not bot.day_trader and analysis['market_session'] == "market_close") or bot.day_trader

    @classmethod
    def _handle_existing_trades(cls, bot, analysis, trades, order, portfolio_item, position, app):
        """Handle trading when existing trades exist for the symbol"""
        last_action_id = trades.action_type_id

        # Check if we can execute orders
        if cls._can_execute_order(bot, analysis):
            # Buy after sell scenario
            if (last_action_id in cls.SELL_ACTIONS + [cls.STOP_LOSS_ACTION] and
                trades.status_id == cls.STATUS_FILLED and
                (analysis['action_type_id'] in cls.BUY_ACTIONS or analysis['action'] == "hold") and
                order['quantity'] > 0):

                print(f"✅ Buy after sell - {analysis['symbol']}")
                return cls.buy(order, app, DT_LIMIT=False)

            # Additional buy during hold
            elif (last_action_id in cls.BUY_ACTIONS and
                  trades.status_id == cls.STATUS_FILLED and
                  (analysis['action_type_id'] in cls.BUY_ACTIONS or analysis['action'] == "hold") and
                  order['quantity'] > 0):

                print(f"✅ Additional buy during hold - {analysis['symbol']}")
                return cls.buy(order, app, DT_LIMIT=False)

        # Handle sell and stop loss logic
        return cls._handle_sell_and_stop_loss_logic(bot, analysis, trades, order, portfolio_item, position, app)

    @classmethod
    def _handle_sell_and_stop_loss_logic(cls, bot, analysis, trades, order, portfolio_item, position, app):
        """Handle sell orders and stop loss logic"""
        from chalicelib.Bots.models import Trades
        import logging
        logger = logging.getLogger(__name__)

        if not portfolio_item.hold and position:
            last_action_id = trades.action_type_id if trades else None
            stop_loss = float(order['stop_loss'])
            closing_price = float(analysis['closing_price'])

            # Direct sell orders
            if analysis['action_type_id'] in cls.SELL_ACTIONS:
                print(f"✅ Executing sell order for {analysis['symbol']}")
                return cls.sell(order, app, DT_LIMIT=False)

            # Stop loss and buy signal handling
            elif analysis['action_type_id'] in cls.BUY_ACTIONS + [cls.STOP_LOSS_ACTION]:
                # Get previous stop loss price if exists
                previous_stop_loss = 0
                if last_action_id == cls.STOP_LOSS_ACTION and trades.price:
                    previous_stop_loss = float(trades.price)

                # Trigger sell if price drops below stop loss
                if (trades and closing_price <= stop_loss and
                    last_action_id not in cls.SELL_ACTIONS + [cls.STOP_LOSS_ACTION] and
                    trades.status_id == cls.STATUS_FILLED):

                    print(f"🔴 Stop Loss triggered: price {closing_price} <= stop loss {stop_loss}")
                    return cls.sell(order, app, DT_LIMIT=False)

                # Trigger sell if price drops below previous stop loss
                elif (trades and closing_price <= previous_stop_loss and
                      last_action_id == cls.STOP_LOSS_ACTION and
                      trades.status_id == cls.STATUS_PENDING):

                    print(f"🔴 Stop Loss triggered: price {closing_price} <= previous stop loss {previous_stop_loss}")
                    return cls.sell(order, app, DT_LIMIT=False)

                # Create stop loss order for filled buy positions
                elif (trades and last_action_id in [1, 5] and  # market_buy, limit_buy
                      trades.status_id == cls.STATUS_FILLED and
                      analysis['action_type_id'] in cls.BUY_ACTIONS):

                    order['trade_uuid'] = trades.uuid
                    print(f"🟡 Creating stop loss order for {analysis['symbol']}")
                    return cls.stop_loss(order, app, DT_LIMIT=False)

                # Handle cancelled stop loss orders
                elif (trades and last_action_id == cls.STOP_LOSS_ACTION and
                      trades.status_id == cls.STATUS_CANCELLED and
                      analysis['action_type_id'] in cls.BUY_ACTIONS + [cls.STOP_LOSS_ACTION]):

                    # Check if there's already a pending stop loss
                    db = app.current_request.db
                    existing_sl = db.query(Trades).filter(
                        Trades.trading_bot_id == bot.id,
                        Trades.symbol == analysis['symbol'],
                        Trades.status_id == cls.STATUS_PENDING,
                        Trades.action_type_id == cls.STOP_LOSS_ACTION
                    ).first()

                    if not existing_sl:
                        order['trade_uuid'] = trades.uuid
                        print(f"🟡 Recreating missing stop loss order for {analysis['symbol']}")
                        return cls.stop_loss(order, app, DT_LIMIT=False)

        return {'success': True, 'message': 'No action taken - conditions not met'}


    @classmethod
    def get_orders(cls, account_uuid, app):
        account = AccountSerializer._get(account_uuid, app)
        db = app.current_request.db
        trades = db.query(Trades).filter(Trades.account_id == account.id).order_by(Trades.order_time.desc()).all()
        response = {}
        response['success'] = True
        response['orders'] = [TradeSchema(t) for t in trades]
        return response


    @classmethod
    def _import_stock_orders(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            automation = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        if automation.bot_type_id == 1:
            updated = ETradeSerializer.import_orders(automation, app)
        elif automation.bot_type_id == 2:
            updated = SchwabSerializer.import_orders(automation, app)
        elif automation.bot_type_id == 3:
            updated = RobinhoodSerializer.import_stock_orders(automation, app)
        elif automation.bot_type_id == 4:
            pass
        elif automation.bot_type_id == 5:
            db.close()
            raise APIException("SignalStack does not return brokerage orders.")
        elif automation.bot_type_id == 6:
            updated = AlpacaSerializer.import_stock_orders(automation, app)

        trades = db.query(Trades).filter(Trades.trading_bot_id == automation.id).order_by(Trades.order_time.asc()).all()
        return trades


    @classmethod
    def import_stock_orders(cls, bot_uuid, app):
        trades = cls._import_stock_orders(bot_uuid, app)
        res = {}
        res['success'] = True
        res['trades'] = [TradeSchema(t) for t in trades]
        return res


    @classmethod
    def automation_performance(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            automation = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            db.close()
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        # print(colored(f"bot id: {automation.id}", "yellow"))

        trades = db.query(Trades).filter(Trades.trading_bot_id == automation.id, Trades.status_id == 5).order_by(Trades.order_time.asc()).all()

        daily = 0
        weekly = 0
        monthly = 0

        total = 0
        count = len(trades)

        for t in trades:
            ## Buys
            if t.action_type_id in [1,5] and t.total_order_cost:
                total = total + float(t.total_order_cost)
            ## Sells and stop losses
            elif t.action_type_id in [2, 8, 9] and t.total_order_cost:
                total = total - float(t.total_order_cost)
            elif t.action_type_id in [9] and t.total_order_cost == None:
                total = total - float(float(t.quantity)*(float(t.limit_stop)))

        # print(colored(f"total: {total}", "yellow"))

        if total > 0 and count > 0:
            daily = total/count
            weekly = total/5
            monthly = total/30

        res = {}
        res['success'] = True
        res['performance'] = {
            "daily": daily,
            "weekly": weekly,
            "monthly": monthly
        }
        return res


    @classmethod
    def profit_check(cls, botUUID, app):
        db = app.current_request.db
        try:
            bot = db.query(TradingBot).filter(TradingBot.uuid == botUUID).one()
        except NoResultFound:
            db.close()
            raise APIException(f"Unable to locate automation with uuid: {botUUID}")

        db = app.current_request.db
        positions = cls._get_positions(bot.uuid, app)

        ## Get he account portfolio daily profit limit
        try:
            account_portfolio = db.query(AccountPortfolio).filter(AccountPortfolio.account_id == bot.account_id).one()
        except NoResultFound:
            db.close()
            raise APIException(f"No account portfolio set for account: {bot.account_id}")

        ## Total up the profits
        total = 0
        for p in positions:
            total = total + p['gain']

        gain_list = []
        for p in positions:
            print(colored(p, 'green'))
            if p['percent_change'] >= account_portfolio.daily_profit_target:
                gain_list.append(p)

        ## Get the automations balance
        automation, balance, available_cash = BotSerializer._get_balance(botUUID, app)

        ## Calculate the daily profit percentage
        if float(balance) > 0 and total > 0:
            profit_pct = (total/float(balance) * 100)
        else:
            profit_pct = 0

        # print("balance", balance)
        # print("total", total)
        # print("profit_pct", profit_pct)

        if profit_pct >= account_portfolio.daily_profit_target:
            # Use stepped take profit for daily profit target
            cls._execute_stepped_take_profit_for_positions(positions, bot, app, "daily_target")
        else:
            # Check individual positions for take profit steps
            cls._check_individual_take_profit_steps(positions, bot, app)
        return {"success": True }

    @classmethod
    def _execute_stepped_take_profit_for_positions(cls, positions, bot, app, trigger_type="daily_target"):
        """Execute stepped take profit for all positions when daily target is reached"""
        from chalicelib.Utils.take_profit_stepping import TakeProfitStepper, create_step_sell_order, log_step_execution
        from chalicelib.Portfolios.models import Portfolio, AccountPortfolio

        db = app.current_request.db

        # Get account portfolio to use the correct take profit target
        try:
            account_portfolio = db.query(AccountPortfolio).filter(AccountPortfolio.account_id == bot.account_id).one()
            take_profit_target = float(account_portfolio.daily_profit_target) if account_portfolio.daily_profit_target else 15.0
        except:
            take_profit_target = 15.0  # Default fallback

        # Create stepper instance with account-specific take profit target
        take_profit_stepper = TakeProfitStepper(base_take_profit_pct=take_profit_target)

        for p in positions:
            try:
                # Get portfolio record to check stepping status
                # Use more specific query to avoid duplicates and handle multiple results gracefully
                try:
                    portfolio_item = db.query(Portfolio).filter(
                        Portfolio.symbol == p['symbol'],
                        Portfolio.account_id == bot.account_id,
                        Portfolio.active == True,
                        Portfolio.status_id == 1  # Only active status
                    ).order_by(Portfolio.created_at.desc()).first()  # Get most recent if multiple exist

                    if not portfolio_item:
                        continue
                except Exception as e:
                    print(f"Error querying portfolio for {p['symbol']}: {e}")
                    continue

                # Initialize stepping fields if not set
                if portfolio_item.original_quantity is None:
                    portfolio_item.original_quantity = portfolio_item.quantity
                    portfolio_item.take_profit_step = 0
                    portfolio_item.step1_sold_quantity = 0
                    portfolio_item.step2_sold_quantity = 0
                    portfolio_item.step3_sold_quantity = 0

                # For daily target, sell remaining position completely
                # Ensure quantity is properly converted to int, handling Decimal types
                try:
                    remaining_quantity = int(float(portfolio_item.quantity)) if portfolio_item.quantity else 0
                except (ValueError, TypeError) as e:
                    print(f"⚠️ Invalid quantity for {p['symbol']}: {portfolio_item.quantity}. Error: {e}")
                    continue
                if remaining_quantity > 0:
                    order = create_step_sell_order(
                        symbol=p['symbol'],
                        step=4,  # Special step for daily target
                        quantity=remaining_quantity,
                        price=float(p['current_price']),
                        account_id=bot.account_id,
                        bot_uuid=bot.uuid
                    )

                    cls.sell(order, app, DT_LIMIT=False)

                    # Update portfolio stepping status
                    portfolio_item.quantity = 0
                    portfolio_item.active = False

                    EventSerializer.create(bot.account_id, 7, f"Daily profit target reached. Sold remaining {remaining_quantity} shares of {p['symbol']}.", app, bot.uuid)

            except Exception as e:
                print(f"Error executing stepped take profit for {p['symbol']}: {e}")
                continue

        try:
            db.commit()
        except:
            db.rollback()

    @classmethod
    def _check_individual_take_profit_steps(cls, positions, bot, app):
        """Check individual positions for take profit step triggers"""
        from chalicelib.Utils.take_profit_stepping import TakeProfitStepper, create_step_sell_order, log_step_execution
        from chalicelib.Portfolios.models import Portfolio, AccountPortfolio

        db = app.current_request.db

        # Get account portfolio to use the correct take profit target
        try:
            account_portfolio = db.query(AccountPortfolio).filter(AccountPortfolio.account_id == bot.account_id).one()
            take_profit_target = float(account_portfolio.daily_profit_target) if account_portfolio.daily_profit_target else 15.0
        except:
            take_profit_target = 15.0  # Default fallback

        # Create stepper instance with account-specific take profit target
        take_profit_stepper = TakeProfitStepper(base_take_profit_pct=take_profit_target)

        for p in positions:
            try:
                # Get portfolio record to check stepping status
                # Use more specific query to avoid duplicates and handle multiple results gracefully
                try:
                    portfolio_item = db.query(Portfolio).filter(
                        Portfolio.symbol == p['symbol'],
                        Portfolio.account_id == bot.account_id,
                        Portfolio.active == True,
                        Portfolio.status_id == 1  # Only active status
                    ).order_by(Portfolio.created_at.desc()).first()  # Get most recent if multiple exist

                    if not portfolio_item or not portfolio_item.buy_price:
                        continue
                except Exception as e:
                    print(f"Error querying portfolio for {p['symbol']}: {e}")
                    continue

                # Initialize stepping fields if not set
                if portfolio_item.original_quantity is None:
                    portfolio_item.original_quantity = portfolio_item.quantity
                    portfolio_item.take_profit_step = 0
                    portfolio_item.step1_sold_quantity = 0
                    portfolio_item.step2_sold_quantity = 0
                    portfolio_item.step3_sold_quantity = 0

                current_price = float(p['current_price'])

                # Validate buy_price
                if portfolio_item.buy_price is None or float(portfolio_item.buy_price) <= 0:
                    print(f"⚠️ Invalid buy_price for {p['symbol']}: {portfolio_item.buy_price}. Skipping take profit check.")
                    continue

                buy_price = float(portfolio_item.buy_price)
                current_step = portfolio_item.take_profit_step or 0

                # Validate current_price
                if current_price <= 0:
                    print(f"⚠️ Invalid current_price for {p['symbol']}: {current_price}. Skipping take profit check.")
                    continue

                # Check if any step should be triggered
                next_step = take_profit_stepper.get_next_step_to_execute(current_price, buy_price, current_step)

                # Debug logging
                step_1_target = take_profit_stepper.get_take_profit_price(buy_price, 1)
                step_2_target = take_profit_stepper.get_take_profit_price(buy_price, 2)
                step_3_target = take_profit_stepper.get_take_profit_price(buy_price, 3)
                print(f"🎯 Take profit check for {p['symbol']}: Current=${current_price:.2f}, Buy=${buy_price:.2f}, Step={current_step}")
                print(f"   Targets: Step1=${step_1_target:.2f}, Step2=${step_2_target:.2f}, Step3=${step_3_target:.2f}")
                print(f"   Next step to execute: {next_step}")

                if next_step:
                    # Safely convert sold quantities to integers
                    try:
                        sold_quantities = [
                            int(float(portfolio_item.step1_sold_quantity or 0)),
                            int(float(portfolio_item.step2_sold_quantity or 0)),
                            int(float(portfolio_item.step3_sold_quantity or 0))
                        ]
                        original_quantity = int(float(portfolio_item.original_quantity or 0))
                    except (ValueError, TypeError) as e:
                        print(f"⚠️ Error converting quantities for {p['symbol']}: {e}")
                        continue

                    step_quantity = take_profit_stepper.calculate_step_quantity(
                        original_quantity, next_step, sold_quantities
                    )

                    if step_quantity > 0:
                        # Check for existing pending orders first
                        try:
                            existing_trade = db.query(Trades).filter(
                                Trades.trading_bot_id == bot.id,
                                Trades.status_id == 4,
                                Trades.action_type_id == 8,
                                Trades.symbol == p['symbol']
                            ).one()
                            if existing_trade:
                                cls.cancel_order(existing_trade.uuid, app)
                        except NoResultFound:
                            pass

                        # Create step sell order
                        order = create_step_sell_order(
                            symbol=p['symbol'],
                            step=next_step,
                            quantity=step_quantity,
                            price=current_price,
                            account_id=bot.account_id,
                            bot_uuid=bot.uuid
                        )

                        cls.sell(order, app, DT_LIMIT=False)

                        # Update portfolio stepping status
                        portfolio_item.take_profit_step = next_step

                        # Ensure proper data type handling for quantity subtraction
                        try:
                            current_quantity = float(portfolio_item.quantity) if portfolio_item.quantity else 0
                            portfolio_item.quantity = current_quantity - step_quantity
                        except (ValueError, TypeError) as e:
                            print(f"⚠️ Error updating quantity for {p['symbol']}: {e}")
                            portfolio_item.quantity = 0  # Safe fallback

                        if next_step == 1:
                            portfolio_item.step1_sold_quantity = step_quantity
                        elif next_step == 2:
                            portfolio_item.step2_sold_quantity = step_quantity
                        elif next_step == 3:
                            portfolio_item.step3_sold_quantity = step_quantity

                        # Check if position is fully closed
                        if portfolio_item.quantity <= 0:
                            portfolio_item.active = False

                        profit_loss = take_profit_stepper.calculate_step_profit_loss(step_quantity, current_price, buy_price)
                        log_step_execution(p['symbol'], next_step, step_quantity, current_price, profit_loss)

                        EventSerializer.create(bot.account_id, 7, f"Take profit step {next_step} triggered. Sold {step_quantity} shares of {p['symbol']} at ${current_price:.2f}.", app, bot.uuid)

            except Exception as e:
                print(f"Error checking take profit steps for {p['symbol']}: {e}")
                continue

        try:
            db.commit()
        except:
            db.rollback()

    @classmethod
    def portfolioProcess(cls, accountUUID, app, DAYTRADE=False):
        from chalicelib.Portfolios.models import PortfolioType, AccountPortfolio, SymbolAction
        from chalicelib.Accounts.models import AccountBalance

        db = app.current_request.db

        ## Get the account
        try:
            account = db.query(Account).filter(Account.uuid == accountUUID).one()
        except NoResultFound:
            db.close()
            raise APIException(f"Unable to process portfolio. Cannot locate account id {accountUUID}.")
        # print(colored(f"Account: {account.id}", "green"))
        # print(colored(f"Account: {account.uuid}", "green"))

        if DAYTRADE:
            print(f"Processing daytrades: {account.id}")

        # 🔍 ENHANCED MARKET SENTIMENT & VOLATILITY CHECK
        # Only run portfolioProcess if sentiment hasn't changed to negative in last 10 hours
        # OR if market volatility is low/downward trend for 3+ hours (override condition)
        try:
            sentiment_check_result = cls._check_portfolio_sentiment_conditions(account.id)

            if not sentiment_check_result["allow_portfolio_processing"]:
                print(f"🚫 Portfolio processing blocked for account {accountUUID}: {sentiment_check_result['reason']}")
                return {
                    "success": True,
                    "processed": [],
                    "blocked": True,
                    "reason": sentiment_check_result["reason"],
                    "sentiment_level": sentiment_check_result.get("current_sentiment_level"),
                    "override_available": sentiment_check_result.get("override_available", False)
                }
            else:
                print(f"✅ Portfolio processing allowed for account {accountUUID}: {sentiment_check_result['reason']}")

        except Exception as e:
            print(f"⚠️ Error checking portfolio sentiment conditions: {e}")
            # On error, allow processing but log the issue
            print("⚠️ Proceeding with portfolio processing due to sentiment check error")

        ## Get the account portfolio
        try:
            accountPortfolio = db.query(AccountPortfolio).filter(AccountPortfolio.account_id == account.id).one()
        except NoResultFound:
            db.close()
            raise APIException(f"Account {accountUUID} does not have an account portfolio created.")
        # print(colored(f"Portfolio: {accountPortfolio.id}", "green"))

        available_cash_ratio = accountPortfolio.available_cash_ratio
        account_portfolio_type = accountPortfolio.portfolio_type_id
        # print(colored(f"Portfolio type id: {account_portfolio_type}", "green"))

        ## Get the current account balance
        balance = db.query(AccountBalance).filter(AccountBalance.account_id == account.id).order_by(AccountBalance.created_at.desc()).first()

        ## Calculate available cash to spend
        if available_cash_ratio == 100:
            return {"success": True}
        available_cash = float(balance.balance) * ((100 - available_cash_ratio)/100)
        # print(colored(f"available_cash: {available_cash}", "blue"))

        ## Update positions
        automations = db.query(TradingBot).filter(TradingBot.account_id == account.id, TradingBot.status_id == 1)
        if DAYTRADE:
            automations = automations.filter(TradingBot.day_trader == True)

        automations = automations.all()

        if DAYTRADE:
            print(f"daytrade automations: {[x.uuid for x in automations]}")

        ## Get the symbols not in cooldown in the portfolio
        try:
            symbols = db.query(Portfolio).filter(Portfolio.account_id == account.id, Portfolio.status_id == 1)
            ## Allow DT if balance over 25k
            if float(balance.balance) < 25000:
                symbols = symbols.filter(Portfolio.cooldown == False).all()
            else:
                symbols = symbols.all()
        except Exception as e:
            print(traceback.format_exc())
            print(e)
            db.close()
            raise APIException("Unable to retreive symbols from portfolio")

        ## Portfolio Types
        try:
            pf_type = db.query(PortfolioType).filter(PortfolioType.id == account_portfolio_type).one()
        except NoResultFound:
            db.close()
            raise APIException("Unable to locate portfolio type on account.")

        ## AI pick portfolio
        ## Set variables
        max_cash = float(available_cash)
        lambda_payload = {
            "multiValueQueryStringParameters": None,
            "headers": {
                "x-api-key": os.environ['AI_API_KEY'],
                "access-token": None,
                "refresh-token": None,
                "account-id": None
            },
            "pathParameters": None,
            "requestContext": {
                "httpMethod": None,
                "resourcePath": None
            },
            "body": None,
            "stageVariables": {
                "stage": os.environ['STAGE']
            }
        }

        ## Calculate the max number of symbols
        max_symbols = 5
        if max_cash >= ********:
            max_symbols = 20
        elif max_cash >= 2500000:
            max_symbols = 17
        elif max_cash >= 1000000:
            max_symbols = 15
        elif max_cash >= 500000:
            max_symbols = 10
        elif max_cash >= 250000:
            max_symbols = 8
        elif max_cash >= 100000:
            max_symbols = 6

        ## Get symbol list
        symbol_list = [x.symbol for x in symbols]
        symbol_list = list(set(symbol_list))
        current_timestamp = unixTimeNow() - 1200

        # Process symbols in batches to avoid MySQL timeout with large IN clauses
        batch_size = 50  # Process 50 symbols at a time
        last_action = []

        for i in range(0, len(symbol_list), batch_size):
            batch_symbols = symbol_list[i:i + batch_size]
            print(f"📊 Processing symbol batch {i//batch_size + 1}/{(len(symbol_list) + batch_size - 1)//batch_size}: {len(batch_symbols)} symbols")

            batch_query = db.query(SymbolAction).filter(
                SymbolAction.symbol.in_(batch_symbols),
                SymbolAction.created_at > current_timestamp,
                SymbolAction.action_type_id.in_([1,3,5])
            ).order_by(SymbolAction.trade_score.desc())

            if DAYTRADE:
                batch_query = batch_query.filter(SymbolAction.day_trade_action == True)
            else:
                batch_query = batch_query.filter(SymbolAction.day_trade_action == False)

            # Limit results per batch to prevent excessive data
            batch_results = batch_query.limit(100).all()
            last_action.extend(batch_results)

        # Use the actual SymbolAction objects instead of just action_body
        valid_actions = []
        for action in last_action:
            if action and action.symbol and action.action_body:
                valid_actions.append(action)
            else:
                print(f"⚠️  Skipping invalid SymbolAction: symbol={getattr(action, 'symbol', None)}, action_body={getattr(action, 'action_body', None)}")

        # actions = sorted(actions, key=lambda d: d['trade_score'], reverse=True)

        if DAYTRADE:
            print(f"daytrade valid_actions count: {len(valid_actions)}")

        action_symbols = set([x.symbol for x in valid_actions])
        # print(colored(f"Action Symbols: {action_symbols}", "yellow"))
        if DAYTRADE:
            print(f"daytrade action_symbols: {action_symbols}")

        ## Get current positions
        current_positions = db.query(Portfolio).filter(Portfolio.active == True, Portfolio.account_id == account.id).all()
        active_symbols = [x.symbol for x in current_positions]
        # print(colored(f"Active Symbols: {active_symbols}", "yellow"))

        ## Compare the two lists and keep the ones that overlap
        keep_list = list(set(action_symbols).intersection(set(active_symbols)))
        drop_list = [x for x in active_symbols if x not in keep_list]
        # print(colored(f"Drop Symbols: {drop_list}", "magenta"))

        ## Sell the symbols in the drop list
        for a in automations:
            positions = cls._get_positions(a.uuid, app)
            # print(colored(f"Positions: {positions}", "blue"))
            if len(positions) > 0:
                for p in positions:
                    if p['symbol'] in drop_list:
                        sym = db.query(Portfolio).filter(Portfolio.active == True, Portfolio.account_id == account.id).one()
                        if sym.hold == False:
                            order = {}
                            order["symbol"] = p['symbol']
                            order["bot_uuid"] = a.uuid
                            order["quantity"] = p['quantity']
                            order['price'] = p['current_price']
                            order['action'] = "sell"
                            order['account_id'] = account.id
                            # print(colored(order, "red"))
                            cls.sell(order, app, DT_LIMIT=False)

        ## If action_symbols is empty and there are not action. Exit
        if len(action_symbols) == 0:
            # print(colored("No actions to take", "green"))
            return {"success": True}

        ## Get keep list count
        symbol_count = len(keep_list)

        ## Get max spend and max number of available symbols to trade
        max_available_symbols = len(valid_actions) + symbol_count
        max_spend_on_symbol = float(max_cash/max_symbols)
        if max_available_symbols == 0:
            raise APIException("No symbols to choose from")

        if max_available_symbols < max_symbols:
            # print(colored(f"max_available_symbols: {max_available_symbols}", "blue"))
            max_spend_on_symbol = float(max_cash/max_available_symbols)
            if max_spend_on_symbol > max_cash * 0.25:
                max_spend_on_symbol = max_cash * 0.25
            max_symbols = max_available_symbols
        # print(colored(f"Max spend on symbol: {max_spend_on_symbol}", "green"))

        ## Get picks with confidence-weighted position sizing
        new_orders = []

        # Collect all actions with their confidence scores
        action_data = []
        for action in valid_actions:
            if symbol_count >= max_symbols:
                break
            if action.symbol not in keep_list:
                # Extract confidence from action_body or use trade_score as fallback
                confidence = action.trade_score / 100.0  # Convert to 0-1 scale
                if action.action_body and 'confidence' in action.action_body:
                    confidence = float(action.action_body['confidence'])

                action_data.append({
                    'symbol': action.symbol,
                    'action': action,
                    'confidence': confidence,
                    'trade_score': action.trade_score
                })

        # Calculate confidence-weighted position sizes
        if action_data:
            total_confidence = sum(item['confidence'] for item in action_data)
            max_position_ratio = 0.40  # 40% maximum per position

            for item in action_data:
                if symbol_count >= max_symbols:
                    break

                # Calculate weighted allocation
                confidence_weight = item['confidence'] / total_confidence if total_confidence > 0 else 1.0 / len(action_data)
                weighted_allocation = available_cash * confidence_weight

                # Apply 40% maximum constraint
                max_position_value = available_cash * max_position_ratio
                position_value = min(weighted_allocation, max_position_value)

                # Ensure minimum viable position size
                min_position_value = available_cash * 0.05  # 5% minimum
                if position_value < min_position_value:
                    position_value = min_position_value

                quantity = int(position_value / float(item['action'].close))

                if quantity > 0:
                    order = item['action'].action_body.copy() if item['action'].action_body else {}
                    order['symbol'] = item['symbol']  # Ensure symbol is included

                    # Set action_type_id and action based on market status
                    market_open = cls.market_status()
                    if market_open:
                        order['action_type_id'] = 1  # Market buy order
                        order['action'] = 'buy'  # Action for signal processing
                    else:
                        order['action_type_id'] = 5  # Limit buy order
                        order['action'] = 'buy'  # Action for signal processing

                    # Add stop_loss and closing_price fields
                    current_price = item.get('current_price', 0)
                    if current_price > 0:
                        order['stop_loss'] = round(current_price * 0.985, 2)  # 1.5% stop loss
                        order['closing_price'] = current_price  # Current price as closing price
                    else:
                        order['stop_loss'] = 0  # Default if no price available
                        order['closing_price'] = 0  # Default if no price available

                    order['account_id'] = account.id
                    order['account_uuid'] = account.uuid
                    order['quantity'] = quantity
                    order['market_session'] = "market_close"
                    order['confidence_weight'] = confidence_weight
                    order['position_value'] = position_value
                    new_orders.append(order)
                    symbol_count = symbol_count + 1

                    print(f"📊 Confidence-weighted order: {item['symbol']} - Confidence: {item['confidence']:.3f}, Weight: {confidence_weight:.3f}, Quantity: {quantity}, Value: ${position_value:.2f}")


        if DAYTRADE:
            print(colored(f"New Orders: {new_orders}", "green"))

        print(f"📋 Portfolio processing summary: {len(new_orders)} orders created for account {account.uuid}")
        for i, order in enumerate(new_orders):
            print(f"  Order {i+1}: {order.get('symbol', 'unknown')} - Action: {order.get('action', 'unknown')} (ID: {order.get('action_type_id', 'unknown')}) - Market Session: {order.get('market_session', 'unknown')}")

        automations = db.query(TradingBot).join(Portfolio, TradingBot.account_id == Portfolio.account_id).filter(Portfolio.status_id == 1, TradingBot.status_id == 1, TradingBot.brokerage_connected == True, TradingBot.account_id == account.id)

        signals_sent = 0
        automation_list = automations.all()

        for a in automation_list:
            for o in new_orders:
                o['bot_uuid'] = a.uuid
                if os.environ['STAGE'] != 'local':
                    lambda_payload['body'] = json.dumps(o)

                    ## Call the lambda
                    try:
                        lambdaName = get_lambda_arn(os.environ['AUTOMATION_SIGNAL_LAMBDA'])
                    except NameError:
                        # Fallback if import fails
                        lambdaName = os.environ['AUTOMATION_SIGNAL_LAMBDA']

                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(lambda_payload)
                    )
                    signals_sent += 1
                    print(f"📤 Signal sent to automation lambda for symbol {o.get('symbol', 'unknown')} - Response: {lambda_response.get('StatusCode', 'unknown')}")
                else:
                    cls.signal(a, o, app)
                    signals_sent += 1

        print(f"🎯 Total signals sent: {signals_sent} (automations: {len(automation_list)}, orders: {len(new_orders)})")

        pick_list = new_orders

        response = {}
        response['success'] = True
        response['processed'] = pick_list
        response['signals_sent'] = signals_sent
        response['automations_count'] = len(automation_list)
        response['orders_count'] = len(new_orders)
        return response


    @classmethod
    def market_status(cls):
        ##############
        ## Returns True for open and False for closed
        ##############
        try:
            headers = {
                "x-api-key": os.environ['AI_API_KEY']
            }
            market_status_url = f"{os.environ['AI_URL']}/markets/status"

            # Add timeout and better error handling
            res = requests.get(url=market_status_url, headers=headers, timeout=10)
            res.raise_for_status()  # Raise exception for HTTP errors

            market_status = res.json()

            try:
                markets = market_status['markets']
                status = False
            except KeyError:
                print(f"⚠️ Market status response missing 'markets' key: {market_status}")
                # Default to market open during trading hours (fallback)
                import datetime
                utc_now = datetime.datetime.utcnow()
                hour = utc_now.hour
                weekday = utc_now.weekday()

                # Assume market is open during typical trading hours (13:30-20:00 UTC, Mon-Fri)
                if weekday < 5 and 13 <= hour < 20:
                    print("ℹ️ Defaulting to market open based on time")
                    return True
                else:
                    print("ℹ️ Defaulting to market closed based on time")
                    return False

            current_status = None
            for m in markets:
                if m['region'] == "United States":
                    current_status = m['current_status']

            if current_status and current_status == "open":
                status = True

            return status

        except requests.exceptions.RequestException as e:
            print(f"⚠️ Market status API request failed: {e}")
            # Fallback to time-based logic
            import datetime
            utc_now = datetime.datetime.utcnow()
            hour = utc_now.hour
            weekday = utc_now.weekday()

            # Assume market is open during typical trading hours (13:30-20:00 UTC, Mon-Fri)
            if weekday < 5 and 13 <= hour < 20:
                print("ℹ️ Fallback: Market assumed open based on time")
                return True
            else:
                print("ℹ️ Fallback: Market assumed closed based on time")
                return False

        except Exception as e:
            print(f"❌ Unexpected error checking market status: {e}")
            # Conservative fallback - assume market is closed
            print("ℹ️ Conservative fallback: Market assumed closed")
            return False


    @classmethod
    def update_balance_sheet(cls, bot_uuid, app):
        db = app.current_request.db
        try:
            bot = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).one()
        except NoResultFound:
            raise APIException(f"Unable to locate automation with id: {bot_uuid}")

        ## Etrade
        if bot.bot_type_id == 1:
            raise APIException("Etrade is currently not supported")

        ## Schwab
        elif bot.bot_type_id == 2:
            raise APIException("Schwab is currently not supported")

        ## Robinhood
        elif bot.bot_type_id == 3:
            raise APIException("Robinhood is currently not supported")

        ## Tradestation
        elif bot.bot_type_id == 4:
            raise APIException("Tradestation is currently not supported")

        ## SignalStack
        elif bot.bot_type_id == 5:
            raise APIException("Cannot track balances or trades for Signal Stack users")

        ## Tradestation
        elif bot.bot_type_id == 6:
            res = AlpacaSerializer.get_todays_profit(bot, app)

        return res


    @classmethod
    def updateProfitCalculations(cls, botUUID, app):
        db = app.current_request.db
        bot = BotSerializer._get_automation(botUUID, app)
        trades = cls._import_stock_orders(botUUID, app)
        for t in trades:
            if t.action_type_id in [2,8,9] and t.status_id == 5:
                BotSerializer._calculate_profit(t, app)

        # print(bot.uuid)
        profitResults = db.query(TradeResult).filter(TradeResult.account_id == bot.account_id)

        ## One Day profits
        timeobj = time.strftime("%m %d %Y 00 00 00")
        month, day, year, hour, minute, seconds = map(int, timeobj.split())
        dt = datetime(year, month, day, hour, minute, seconds)
        start_of_day = int(dt.astimezone(timezone.utc).timestamp())
        one_day_profits_query = profitResults.filter(TradeResult.sell_date > start_of_day, TradeResult.sell_date <= unixTimeNow()).all()
        one_day_profits = 0
        for d in one_day_profits_query:
            one_day_profits = one_day_profits + d.profit

        ## Yesterday Profits
        end_of_yesterday = start_of_day - 1
        beginning_of_yesterday_dt = datetime(year, month, day-1, hour, minute, seconds)
        beginning_of_yesterday = int(beginning_of_yesterday_dt.astimezone(timezone.utc).timestamp())
        yesterday_profits_query = profitResults.filter(TradeResult.sell_date > beginning_of_yesterday, TradeResult.sell_date <= end_of_yesterday).all()
        yesterday_profits = 0
        for y in yesterday_profits_query:
            yesterday_profits = yesterday_profits + y.profit

        ## Current Month Profits
        current_month_dt = datetime(year, month, 1, 0, 0, 0)
        current_month = int(current_month_dt.astimezone(timezone.utc).timestamp())
        current_month_profits_query = profitResults.filter(TradeResult.sell_date > current_month, TradeResult.sell_date <= unixTimeNow()).all()
        current_month_profits = 0
        for cm in current_month_profits_query:
            current_month_profits = current_month_profits + cm.profit

        ## Last month profits
        now = datetime.now()
        beginning_of_last_month_dt = now - relativedelta(months = 1)
        beginning_of_last_month_dt = datetime(beginning_of_last_month_dt.year, beginning_of_last_month_dt.month, 1, 0, 0, 0)
        beginning_of_last_month = int(beginning_of_last_month_dt.astimezone(timezone.utc).timestamp())
        end_of_last_month_dt = current_month - 1
        last_month_profits_query = profitResults.filter(TradeResult.sell_date > beginning_of_last_month, TradeResult.sell_date <= end_of_last_month_dt).all()
        last_month_profits = 0
        for lm in last_month_profits_query:
            last_month_profits = last_month_profits + lm.profit


        ## YTD profits
        now = datetime.now()
        beginning_of_year_dt = datetime(now.year, 1, 1, 0, 0, 0)
        beginning_of_year = int(beginning_of_year_dt.astimezone(timezone.utc).timestamp())
        ytd_profits_query = profitResults.filter(TradeResult.sell_date > beginning_of_year, TradeResult.sell_date <= now).all()
        ytd_profits = 0
        for lm in ytd_profits_query:
            ytd_profits = ytd_profits + lm.profit

        res = {}
        res['success'] = True
        res['today_profit'] = one_day_profits
        res['yesterday_profit'] = yesterday_profits
        res['current_month_profit'] = current_month_profits
        res['last_month_profit'] = last_month_profits
        res['ytd_profit'] = last_month_profits
        return res

    @classmethod
    def _check_portfolio_sentiment_conditions(cls, account_id):
        """
        Check if portfolioProcess should be allowed to run based on market sentiment and volatility conditions.

        Rules:
        1. Block if sentiment changed to very negative in last 10 hours
        2. Allow override if market volatility is low OR obvious downward trend for 3+ hours

        Args:
            account_id (int): Account ID for logging purposes

        Returns:
            dict: Decision result with reasoning
        """
        from chalicelib.MarketSentiment.models import MarketSentiment
        from chalicelib.Utils.functions import unixTimeNow
        from chalicelib.Utils.database import conn
        from sqlalchemy import desc
        import requests
        import os

        try:
            print(f"🔍 Checking portfolio sentiment conditions for account {account_id}")

            # Get current time and 10-hour cutoff
            current_time = unixTimeNow()
            ten_hours_ago = current_time - (10 * 3600)  # 10 hours in seconds
            three_hours_ago = current_time - (3 * 3600)  # 3 hours in seconds

            # Get recent market sentiment history (last 10 hours)
            db = conn()

            try:
                recent_sentiments = db.query(MarketSentiment).filter(
                    MarketSentiment.analysis_timestamp >= ten_hours_ago,
                    MarketSentiment.status_id == 1
                ).order_by(desc(MarketSentiment.analysis_timestamp)).all()

                if not recent_sentiments:
                    print("⚠️ No recent sentiment data found - allowing portfolio processing")
                    return {
                        "allow_portfolio_processing": True,
                        "reason": "No recent sentiment data available - proceeding with caution",
                        "current_sentiment_level": "Unknown"
                    }

                # Get current sentiment
                current_sentiment = recent_sentiments[0]
                current_sentiment_level = current_sentiment.sentiment_level

                print(f"📊 Current market sentiment: {current_sentiment_level} (Score: {current_sentiment.sentiment_score})")

                # Check if sentiment changed to very negative in last 10 hours
                sentiment_became_very_negative = False
                negative_change_time = None

                for i, sentiment in enumerate(recent_sentiments):
                    if sentiment.sentiment_level in ["Very Negative"]:
                        # Check if previous sentiment was better (not very negative)
                        if i < len(recent_sentiments) - 1:
                            previous_sentiment = recent_sentiments[i + 1]
                            if previous_sentiment.sentiment_level not in ["Very Negative"]:
                                sentiment_became_very_negative = True
                                negative_change_time = sentiment.analysis_timestamp
                                print(f"🔴 Sentiment changed to very negative at {negative_change_time}: {previous_sentiment.sentiment_level} → {sentiment.sentiment_level}")
                                break

                # If sentiment became very negative in last 10 hours, check for override conditions
                if sentiment_became_very_negative:
                    print(f"⚠️ Sentiment became very negative in last 10 hours - checking override conditions...")

                    # Check override conditions: low volatility OR downward trend for 3+ hours
                    override_result = cls._check_volatility_override_conditions(three_hours_ago, current_time)

                    # NEW: Check for symbol-level positive sentiment exceptions
                    symbol_sentiment_override = cls._check_symbol_sentiment_exceptions(account_id, three_hours_ago, current_time)

                    if override_result["allow_override"]:
                        print(f"✅ Market override conditions met: {override_result['reason']}")
                        return {
                            "allow_portfolio_processing": True,
                            "reason": f"Sentiment became very negative but market override conditions met: {override_result['reason']}",
                            "current_sentiment_level": current_sentiment_level,
                            "override_applied": True,
                            "override_type": "market_conditions",
                            "override_reason": override_result["reason"]
                        }
                    elif symbol_sentiment_override["allow_override"]:
                        print(f"✅ Symbol sentiment override conditions met: {symbol_sentiment_override['reason']}")
                        return {
                            "allow_portfolio_processing": True,
                            "reason": f"Sentiment became very negative but symbol sentiment override conditions met: {symbol_sentiment_override['reason']}",
                            "current_sentiment_level": current_sentiment_level,
                            "override_applied": True,
                            "override_type": "symbol_sentiment",
                            "override_reason": symbol_sentiment_override["reason"],
                            "positive_symbols": symbol_sentiment_override.get("positive_symbols", [])
                        }
                    else:
                        print(f"🚫 No override conditions met. Market: {override_result['reason']}. Symbols: {symbol_sentiment_override['reason']}")
                        return {
                            "allow_portfolio_processing": False,
                            "reason": f"Market sentiment changed to {current_sentiment_level} in last 10 hours and no override conditions met",
                            "current_sentiment_level": current_sentiment_level,
                            "override_available": False,
                            "market_override_reason": override_result["reason"],
                            "symbol_override_reason": symbol_sentiment_override["reason"]
                        }

                # Sentiment hasn't become negative recently - allow processing
                print(f"✅ No recent negative sentiment changes detected")
                return {
                    "allow_portfolio_processing": True,
                    "reason": f"Market sentiment ({current_sentiment_level}) has not changed to negative in last 10 hours",
                    "current_sentiment_level": current_sentiment_level
                }

            finally:
                db.close()

        except Exception as e:
            print(f"❌ Error checking portfolio sentiment conditions: {e}")
            import traceback
            traceback.print_exc()

            # On error, allow processing but log the issue
            return {
                "allow_portfolio_processing": True,
                "reason": f"Error checking sentiment conditions: {str(e)} - allowing processing",
                "current_sentiment_level": "Error",
                "error": str(e)
            }

    @classmethod
    def _check_volatility_override_conditions(cls, three_hours_ago, current_time):
        """
        Check if market volatility or trend conditions allow overriding negative sentiment.

        Override conditions:
        1. Market volatility is low (VIX < 20 or market stability indicators)
        2. Obvious downward trend for 3+ hours (consistent decline in major indices)

        Args:
            three_hours_ago (int): Unix timestamp 3 hours ago
            current_time (int): Current unix timestamp

        Returns:
            dict: Override decision with reasoning
        """
        import requests
        import os

        try:
            print(f"🔍 Checking volatility override conditions...")

            # Check 1: Market Volatility (VIX data)
            volatility_override = cls._check_market_volatility()

            # Check 2: Market Trend Analysis (3+ hour downward trend)
            trend_override = cls._check_market_trend(three_hours_ago, current_time)

            # Allow override if either condition is met
            if volatility_override["allow_override"]:
                return {
                    "allow_override": True,
                    "reason": f"Low market volatility override: {volatility_override['reason']}",
                    "volatility_data": volatility_override,
                    "trend_data": trend_override
                }
            elif trend_override["allow_override"]:
                return {
                    "allow_override": True,
                    "reason": f"Downward trend override: {trend_override['reason']}",
                    "volatility_data": volatility_override,
                    "trend_data": trend_override
                }
            else:
                return {
                    "allow_override": False,
                    "reason": f"No override conditions met. Volatility: {volatility_override['reason']}. Trend: {trend_override['reason']}",
                    "volatility_data": volatility_override,
                    "trend_data": trend_override
                }

        except Exception as e:
            print(f"❌ Error checking volatility override conditions: {e}")
            return {
                "allow_override": False,
                "reason": f"Error checking override conditions: {str(e)}",
                "error": str(e)
            }

    @classmethod
    def _check_market_volatility(cls):
        """
        Check if market volatility is low enough to override negative sentiment.
        Uses VIX data and market stability indicators.

        Returns:
            dict: Volatility analysis result
        """
        try:
            print(f"📊 Checking market volatility...")

            # Try to get VIX data from AlphaAdvantage
            api_key = os.environ.get('AA_API_KEY', 'VKV7S2P3F4QD1FMU')

            # Get VIX (volatility index) data
            vix_url = f"https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=VIX&apikey={api_key}"

            try:
                response = requests.get(vix_url, timeout=10)
                vix_data = response.json()

                if 'Time Series (Daily)' in vix_data:
                    # Get the most recent VIX value
                    time_series = vix_data['Time Series (Daily)']
                    latest_date = max(time_series.keys())
                    latest_vix = float(time_series[latest_date]['4. close'])

                    print(f"📊 Current VIX: {latest_vix}")

                    # VIX interpretation:
                    # < 20: Low volatility (good for override)
                    # 20-30: Moderate volatility
                    # > 30: High volatility

                    if latest_vix < 20:
                        return {
                            "allow_override": True,
                            "reason": f"Low market volatility (VIX: {latest_vix:.2f} < 20)",
                            "vix_value": latest_vix,
                            "volatility_level": "Low"
                        }
                    elif latest_vix < 25:
                        return {
                            "allow_override": False,
                            "reason": f"Moderate market volatility (VIX: {latest_vix:.2f})",
                            "vix_value": latest_vix,
                            "volatility_level": "Moderate"
                        }
                    else:
                        return {
                            "allow_override": False,
                            "reason": f"High market volatility (VIX: {latest_vix:.2f} >= 25)",
                            "vix_value": latest_vix,
                            "volatility_level": "High"
                        }
                else:
                    print(f"⚠️ No VIX data available: {vix_data}")

            except Exception as e:
                print(f"⚠️ Error fetching VIX data: {e}")

            # Fallback: Use SPY volatility as proxy
            spy_volatility = cls._calculate_spy_volatility()
            if spy_volatility["success"]:
                if spy_volatility["volatility"] < 0.015:  # 1.5% daily volatility
                    return {
                        "allow_override": True,
                        "reason": f"Low SPY volatility ({spy_volatility['volatility']:.3f} < 0.015)",
                        "spy_volatility": spy_volatility["volatility"],
                        "volatility_level": "Low"
                    }
                else:
                    return {
                        "allow_override": False,
                        "reason": f"High SPY volatility ({spy_volatility['volatility']:.3f} >= 0.015)",
                        "spy_volatility": spy_volatility["volatility"],
                        "volatility_level": "High"
                    }

            # If all else fails, be conservative
            return {
                "allow_override": False,
                "reason": "Unable to determine market volatility - being conservative",
                "volatility_level": "Unknown"
            }

        except Exception as e:
            print(f"❌ Error checking market volatility: {e}")
            return {
                "allow_override": False,
                "reason": f"Error checking volatility: {str(e)}",
                "error": str(e)
            }

    @classmethod
    def _calculate_spy_volatility(cls):
        """
        Calculate SPY volatility as a proxy for market volatility.

        Returns:
            dict: SPY volatility calculation result
        """
        try:
            api_key = os.environ.get('AA_API_KEY', 'VKV7S2P3F4QD1FMU')
            spy_url = f"https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=SPY&apikey={api_key}&outputsize=compact"

            response = requests.get(spy_url, timeout=10)
            spy_data = response.json()

            if 'Time Series (Daily)' in spy_data:
                time_series = spy_data['Time Series (Daily)']
                dates = sorted(time_series.keys(), reverse=True)[:10]  # Last 10 days

                returns = []
                for i in range(1, len(dates)):
                    current_close = float(time_series[dates[i-1]]['4. close'])
                    previous_close = float(time_series[dates[i]]['4. close'])
                    daily_return = (current_close - previous_close) / previous_close
                    returns.append(daily_return)

                if returns:
                    import statistics
                    volatility = statistics.stdev(returns)
                    return {
                        "success": True,
                        "volatility": volatility,
                        "days_analyzed": len(returns)
                    }

            return {"success": False, "error": "No SPY data available"}

        except Exception as e:
            print(f"⚠️ Error calculating SPY volatility: {e}")
            return {"success": False, "error": str(e)}

    @classmethod
    def _check_market_trend(cls, three_hours_ago, current_time):
        """
        Check if there's been an obvious downward trend for 3+ hours.

        Args:
            three_hours_ago (int): Unix timestamp 3 hours ago
            current_time (int): Current unix timestamp

        Returns:
            dict: Trend analysis result
        """
        try:
            print(f"📈 Checking market trend over last 3 hours...")

            # Get SPY intraday data to check for downward trend
            api_key = os.environ.get('AA_API_KEY', 'VKV7S2P3F4QD1FMU')

            # Use intraday data (5min intervals) to check trend
            spy_intraday_url = f"https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol=SPY&interval=5min&apikey={api_key}&outputsize=compact"

            try:
                response = requests.get(spy_intraday_url, timeout=10)
                spy_data = response.json()

                if 'Time Series (5min)' in spy_data:
                    time_series = spy_data['Time Series (5min)']

                    # Convert timestamps and filter for last 3 hours
                    from datetime import datetime, timedelta
                    import time

                    current_dt = datetime.fromtimestamp(current_time)
                    three_hours_ago_dt = datetime.fromtimestamp(three_hours_ago)

                    recent_prices = []
                    for timestamp_str, data in time_series.items():
                        try:
                            timestamp_dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            if three_hours_ago_dt <= timestamp_dt <= current_dt:
                                recent_prices.append({
                                    'timestamp': timestamp_dt,
                                    'close': float(data['4. close'])
                                })
                        except ValueError:
                            continue

                    # Sort by timestamp
                    recent_prices.sort(key=lambda x: x['timestamp'])

                    if len(recent_prices) >= 10:  # Need at least 10 data points (50 minutes)
                        # Calculate trend using linear regression
                        prices = [p['close'] for p in recent_prices]

                        # Simple trend calculation: compare first half vs second half
                        mid_point = len(prices) // 2
                        first_half_avg = sum(prices[:mid_point]) / mid_point
                        second_half_avg = sum(prices[mid_point:]) / (len(prices) - mid_point)

                        trend_change = (second_half_avg - first_half_avg) / first_half_avg

                        print(f"📈 3-hour trend change: {trend_change:.4f} ({trend_change*100:.2f}%)")

                        # Consider it a downward trend if decline > 1% over 3 hours
                        if trend_change < -0.01:  # -1% or more decline
                            return {
                                "allow_override": True,
                                "reason": f"Obvious downward trend detected: {trend_change*100:.2f}% decline over 3 hours",
                                "trend_change": trend_change,
                                "data_points": len(recent_prices),
                                "trend_direction": "Downward"
                            }
                        elif trend_change < -0.005:  # -0.5% decline
                            return {
                                "allow_override": False,
                                "reason": f"Mild downward trend: {trend_change*100:.2f}% decline (not significant enough)",
                                "trend_change": trend_change,
                                "data_points": len(recent_prices),
                                "trend_direction": "Slightly Downward"
                            }
                        else:
                            return {
                                "allow_override": False,
                                "reason": f"No significant downward trend: {trend_change*100:.2f}% change",
                                "trend_change": trend_change,
                                "data_points": len(recent_prices),
                                "trend_direction": "Stable/Upward"
                            }
                    else:
                        return {
                            "allow_override": False,
                            "reason": f"Insufficient data points for trend analysis ({len(recent_prices)} < 10)",
                            "data_points": len(recent_prices)
                        }

            except Exception as e:
                print(f"⚠️ Error fetching SPY intraday data: {e}")

            # Fallback: Check daily trend using recent daily data
            return cls._check_daily_trend_fallback()

        except Exception as e:
            print(f"❌ Error checking market trend: {e}")
            return {
                "allow_override": False,
                "reason": f"Error checking trend: {str(e)}",
                "error": str(e)
            }

    @classmethod
    def _check_daily_trend_fallback(cls):
        """
        Fallback trend check using daily data when intraday data is unavailable.

        Returns:
            dict: Daily trend analysis result
        """
        try:
            print(f"📈 Using daily trend fallback analysis...")

            api_key = os.environ.get('AA_API_KEY', 'VKV7S2P3F4QD1FMU')
            spy_url = f"https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=SPY&apikey={api_key}&outputsize=compact"

            response = requests.get(spy_url, timeout=10)
            spy_data = response.json()

            if 'Time Series (Daily)' in spy_data:
                time_series = spy_data['Time Series (Daily)']
                dates = sorted(time_series.keys(), reverse=True)[:5]  # Last 5 days

                if len(dates) >= 3:
                    # Check if last 3 days show consistent decline
                    prices = [float(time_series[date]['4. close']) for date in dates[:3]]

                    # Check if each day is lower than the previous
                    declining_days = 0
                    for i in range(1, len(prices)):
                        if prices[i] < prices[i-1]:
                            declining_days += 1

                    total_change = (prices[-1] - prices[0]) / prices[0]

                    if declining_days >= 2 and total_change < -0.02:  # 2+ declining days and >2% total decline
                        return {
                            "allow_override": True,
                            "reason": f"Multi-day downward trend: {declining_days} declining days, {total_change*100:.2f}% total decline",
                            "declining_days": declining_days,
                            "total_change": total_change,
                            "trend_direction": "Downward"
                        }
                    else:
                        return {
                            "allow_override": False,
                            "reason": f"No significant daily downward trend: {declining_days} declining days, {total_change*100:.2f}% change",
                            "declining_days": declining_days,
                            "total_change": total_change,
                            "trend_direction": "Mixed/Stable"
                        }

            return {
                "allow_override": False,
                "reason": "Unable to determine daily trend - no data available",
                "trend_direction": "Unknown"
            }

        except Exception as e:
            print(f"❌ Error in daily trend fallback: {e}")
            return {
                "allow_override": False,
                "reason": f"Error in daily trend analysis: {str(e)}",
                "error": str(e)
            }

    @classmethod
    def _check_symbol_sentiment_exceptions(cls, account_id, three_hours_ago, current_time):
        """
        Check for symbol-level sentiment exceptions that allow portfolio processing
        even when market sentiment is negative.

        Exception conditions:
        1. Symbol sentiment is "Very Positive"
        2. Symbol sentiment became "Positive" from neutral/negative/very negative in last 3 hours

        Args:
            account_id (int): Account ID to check portfolio symbols for
            three_hours_ago (int): Unix timestamp 3 hours ago
            current_time (int): Current unix timestamp

        Returns:
            dict: Symbol sentiment exception result
        """
        from chalicelib.MarketSentiment.models import SymbolSentiment
        from chalicelib.Portfolios.models import Portfolio
        from chalicelib.Utils.database import conn
        from sqlalchemy import desc, and_

        try:
            print(f"🔍 Checking symbol sentiment exceptions for account {account_id}...")

            db = conn()

            try:
                # Get all active portfolio symbols for this account
                portfolio_symbols = db.query(Portfolio).filter(
                    Portfolio.account_id == account_id,
                    Portfolio.status_id == 1,  # Active symbols only
                    Portfolio.active == True
                ).all()

                if not portfolio_symbols:
                    print(f"⚠️ No active portfolio symbols found for account {account_id}")
                    return {
                        "allow_override": False,
                        "reason": "No active portfolio symbols to check for sentiment exceptions",
                        "symbols_checked": 0
                    }

                symbol_list = [p.symbol for p in portfolio_symbols]
                print(f"📊 Checking sentiment for {len(symbol_list)} portfolio symbols: {symbol_list}")

                positive_symbols = []
                very_positive_symbols = []
                sentiment_improved_symbols = []

                # Check each symbol's sentiment
                for symbol in symbol_list:
                    try:
                        symbol_exception = cls._check_individual_symbol_sentiment(symbol, three_hours_ago, current_time, db)

                        if symbol_exception["is_very_positive"]:
                            very_positive_symbols.append({
                                "symbol": symbol,
                                "sentiment_level": symbol_exception["current_sentiment_level"],
                                "sentiment_score": symbol_exception["current_sentiment_score"],
                                "reason": "Very Positive sentiment"
                            })

                        elif symbol_exception["became_positive"]:
                            sentiment_improved_symbols.append({
                                "symbol": symbol,
                                "previous_sentiment": symbol_exception["previous_sentiment_level"],
                                "current_sentiment": symbol_exception["current_sentiment_level"],
                                "sentiment_score": symbol_exception["current_sentiment_score"],
                                "change_time": symbol_exception["change_time"],
                                "reason": f"Became positive from {symbol_exception['previous_sentiment_level']}"
                            })

                        if symbol_exception["is_very_positive"] or symbol_exception["became_positive"]:
                            positive_symbols.append(symbol)

                    except Exception as e:
                        print(f"⚠️ Error checking sentiment for symbol {symbol}: {e}")
                        continue

                # Determine if we have enough positive symbols to allow override
                total_positive = len(positive_symbols)
                total_symbols = len(symbol_list)

                print(f"📊 Symbol sentiment summary:")
                print(f"   Very Positive: {len(very_positive_symbols)} symbols")
                print(f"   Became Positive: {len(sentiment_improved_symbols)} symbols")
                print(f"   Total Positive: {total_positive}/{total_symbols} symbols")

                # Allow override if we have any very positive symbols OR any symbols that became positive
                if very_positive_symbols or sentiment_improved_symbols:
                    reason_parts = []
                    if very_positive_symbols:
                        symbols_str = ", ".join([s["symbol"] for s in very_positive_symbols])
                        reason_parts.append(f"{len(very_positive_symbols)} very positive symbols ({symbols_str})")
                    if sentiment_improved_symbols:
                        symbols_str = ", ".join([s["symbol"] for s in sentiment_improved_symbols])
                        reason_parts.append(f"{len(sentiment_improved_symbols)} symbols became positive ({symbols_str})")

                    reason = " and ".join(reason_parts)

                    return {
                        "allow_override": True,
                        "reason": f"Symbol sentiment exceptions found: {reason}",
                        "positive_symbols": positive_symbols,
                        "very_positive_symbols": very_positive_symbols,
                        "sentiment_improved_symbols": sentiment_improved_symbols,
                        "symbols_checked": total_symbols,
                        "positive_ratio": total_positive / total_symbols if total_symbols > 0 else 0
                    }
                else:
                    return {
                        "allow_override": False,
                        "reason": f"No symbol sentiment exceptions found among {total_symbols} portfolio symbols",
                        "positive_symbols": [],
                        "symbols_checked": total_symbols,
                        "positive_ratio": 0
                    }

            finally:
                db.close()

        except Exception as e:
            print(f"❌ Error checking symbol sentiment exceptions: {e}")
            import traceback
            traceback.print_exc()

            return {
                "allow_override": False,
                "reason": f"Error checking symbol sentiment exceptions: {str(e)}",
                "error": str(e)
            }

    @classmethod
    def _check_individual_symbol_sentiment(cls, symbol, three_hours_ago, current_time, db):
        """
        Check sentiment conditions for an individual symbol.

        Args:
            symbol (str): Stock symbol to check
            three_hours_ago (int): Unix timestamp 3 hours ago
            current_time (int): Current unix timestamp
            db: Database session

        Returns:
            dict: Individual symbol sentiment analysis
        """
        from chalicelib.MarketSentiment.models import SymbolSentiment
        from sqlalchemy import desc

        try:
            # Get recent symbol sentiment data (last 3 hours)
            recent_symbol_sentiments = db.query(SymbolSentiment).filter(
                SymbolSentiment.symbol == symbol,
                SymbolSentiment.analysis_timestamp >= three_hours_ago,
                SymbolSentiment.status_id == 1
            ).order_by(desc(SymbolSentiment.analysis_timestamp)).all()

            if not recent_symbol_sentiments:
                # No recent sentiment data - check if we have any historical data
                latest_sentiment = db.query(SymbolSentiment).filter(
                    SymbolSentiment.symbol == symbol,
                    SymbolSentiment.status_id == 1
                ).order_by(desc(SymbolSentiment.analysis_timestamp)).first()

                if latest_sentiment and latest_sentiment.sentiment_level == "Very Positive":
                    print(f"📊 {symbol}: Using latest available sentiment (Very Positive)")
                    return {
                        "is_very_positive": True,
                        "became_positive": False,
                        "current_sentiment_level": latest_sentiment.sentiment_level,
                        "current_sentiment_score": float(latest_sentiment.sentiment_score),
                        "data_age": "historical"
                    }
                else:
                    print(f"⚠️ {symbol}: No recent sentiment data available")
                    return {
                        "is_very_positive": False,
                        "became_positive": False,
                        "current_sentiment_level": "Unknown",
                        "current_sentiment_score": 0.0,
                        "data_age": "none"
                    }

            # Get current (most recent) sentiment
            current_sentiment = recent_symbol_sentiments[0]
            current_sentiment_level = current_sentiment.sentiment_level
            current_sentiment_score = float(current_sentiment.sentiment_score)

            print(f"📊 {symbol}: Current sentiment = {current_sentiment_level} (Score: {current_sentiment_score:.3f})")

            # Check if current sentiment is "Very Positive"
            is_very_positive = current_sentiment_level == "Very Positive"

            # Check if sentiment became "Positive" from neutral/negative/very negative in last 3 hours
            became_positive = False
            previous_sentiment_level = None
            change_time = None

            if current_sentiment_level == "Positive":
                # Look for previous sentiment that was not positive
                for i, sentiment in enumerate(recent_symbol_sentiments):
                    if sentiment.sentiment_level == "Positive":
                        continue  # Skip other positive entries
                    else:
                        # Found a non-positive sentiment - check if it was neutral/negative/very negative
                        if sentiment.sentiment_level in ["Neutral", "Negative", "Very Negative"]:
                            became_positive = True
                            previous_sentiment_level = sentiment.sentiment_level
                            change_time = sentiment.analysis_timestamp
                            print(f"📈 {symbol}: Became positive from {previous_sentiment_level} at {change_time}")
                            break

            return {
                "is_very_positive": is_very_positive,
                "became_positive": became_positive,
                "current_sentiment_level": current_sentiment_level,
                "current_sentiment_score": current_sentiment_score,
                "previous_sentiment_level": previous_sentiment_level,
                "change_time": change_time,
                "data_points": len(recent_symbol_sentiments)
            }

        except Exception as e:
            print(f"❌ Error checking individual symbol sentiment for {symbol}: {e}")
            return {
                "is_very_positive": False,
                "became_positive": False,
                "current_sentiment_level": "Error",
                "current_sentiment_score": 0.0,
                "error": str(e)
            }
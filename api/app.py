import json, os, boto3, logging, traceback, time, signal, atexit
from aws_xray_sdk.core import xray_recorder, patch_all
from chalice import Chalice, CORSConfig, Cron, Rate
from sqlalchemy import or_
from chalicelib.Utils.decorators import Decorators
from chalicelib.Authentication.serializer import Authentication
from chalicelib.Accounts.serializer import AccountSerializer
from chalicelib.Authentication.cognito import cognito
from chalicelib.Contact.serializer import Contact
from chalicelib.Portfolios.serializer import PortfolioSerializer
from chalicelib.Bots.serializer import BotSerializer, AutomationSerializer
from chalicelib.Bots.Etrade.serializer import ETradeSerializer
from chalicelib.Bots.Schwab.serializer import SchwabSerializer, SchwabWrapper
from chalicelib.Subscriptions.serializer import SubscriptionSerializer
from chalicelib.Users.serializer import UsersSerializer
from chalicelib.Sagemaker.serializer import SageMakerSerializer
from chalicelib.Logs.serializer import EventSerializer
from termcolor import colored
from chalicelib.SymbolLists.serializer import SymbolSerializer, SymbolListSerializer
from chalicelib.Utils.database import cleanup_on_lambda_timeout, force_close_all_connections, lambda_connection_cleanup
from chalicelib.Utils.exceptions import APIException
from chalicelib.Utils.health_check import (
    health_check_endpoint, performance_test_endpoint,
    circuit_breaker_status_endpoint
)

app = Chalice(app_name='thriving')
app.debug = True

try:
    import localSettings
except ModuleNotFoundError:
    pass

corsConfig = CORSConfig(
    allow_origin='*',
    allow_headers=["Content-Type", "X-Api-Key", "access-token", "refresh-token", "token"]
)

if os.environ['STAGE'] != 'local':
    xray_recorder.configure(context_missing='LOG_ERROR')
    patch_all()

# Setup signal handlers for graceful shutdown and timeout handling
def signal_handler(signum, frame):
    """Handle Lambda timeout and shutdown signals"""
    print(f"🚨 Signal {signum} received - cleaning up database connections")
    cleanup_on_lambda_timeout()

def emergency_cleanup():
    """Emergency cleanup function for atexit"""
    print("🚨 Lambda container shutting down - emergency cleanup")
    force_close_all_connections()

# Register signal handlers (Lambda uses SIGTERM for timeout)
try:
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    atexit.register(emergency_cleanup)
    print("✅ Database cleanup signal handlers registered")
except Exception as e:
    print(f"⚠️  Could not register signal handlers: {e}")

apiDecorator = Decorators(app)

@app.route('/')
def index():
    return {'hello': 'world'}

@apiDecorator.protectedRoute('/admin/db-connection-info', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDbConnectionInfo():
    """Get database connection pool information for monitoring"""
    from chalicelib.Utils.database import get_connection_info

    try:
        connection_info = get_connection_info()
        return {
            'success': True,
            'connection_info': connection_info
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@apiDecorator.protectedRoute('/admin/db-connection-refresh', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def refreshDbConnection():
    """Force refresh the container database session"""
    from chalicelib.Utils.database import refresh_container_session, get_connection_info

    try:
        # Refresh the session
        new_session = refresh_container_session()

        # Get updated connection info
        connection_info = get_connection_info()

        return {
            'success': True,
            'message': 'Container session refreshed successfully',
            'connection_info': connection_info
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@apiDecorator.protectedRoute('/admin/db-connection-force-close', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def forceCloseDbConnections():
    """Force close all database connections (emergency use only)"""
    try:
        force_close_all_connections()

        # Get updated connection info
        from chalicelib.Utils.database import get_connection_info
        connection_info = get_connection_info()

        return {
            'success': True,
            'message': 'All database connections force closed',
            'connection_info': connection_info
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@apiDecorator.protectedRoute('/admin/db-connection-cleanup', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def cleanupDbConnections():
    """Perform Lambda timeout cleanup (for testing)"""
    try:
        cleanup_on_lambda_timeout()

        # Get updated connection info
        from chalicelib.Utils.database import get_connection_info
        connection_info = get_connection_info()

        return {
            'success': True,
            'message': 'Lambda timeout cleanup performed',
            'connection_info': connection_info
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

#################################################################
###########           Database Health Checks  ###################
#################################################################
@apiDecorator.protectedRoute('/admin/db-health', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDatabaseHealth():
    """Get comprehensive database health status"""
    return health_check_endpoint()

@apiDecorator.protectedRoute('/admin/db-performance-test', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def runDatabasePerformanceTest():
    """Run database performance test on all tiers"""
    return performance_test_endpoint()

@apiDecorator.protectedRoute('/admin/db-circuit-breakers', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getCircuitBreakerStatus():
    """Get circuit breaker status for all tiers"""
    return circuit_breaker_status_endpoint()

@apiDecorator.protectedRoute('/admin/db-v2-status', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDatabaseV2Status():
    """Get status of the new database v2 system"""
    try:
        from chalicelib.Utils.database_v2 import get_connection_health_status, warm_up_connections

        # Warm up connections
        warm_up_connections()

        # Get health status
        health_status = get_connection_health_status()

        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'database_v2_available': True,
                'health_status': health_status,
                'message': 'Database v2 system is operational'
            }, indent=2)
        }
    except ImportError:
        return {
            'statusCode': 503,
            'body': json.dumps({
                'success': False,
                'database_v2_available': False,
                'error': 'Database v2 system not available'
            }, indent=2)
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e)
            }, indent=2)
        }

#################################################################
###########           Authentication          ###################
#################################################################
@apiDecorator.publicRoute('/login', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def authenticate():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db

            print(app.current_request.__dict__)
            if app.current_request._body:
                data = app.current_request._body
            else:
                data = app.current_request.json_body

            response = Authentication.login(data, app)
            return response
    except Exception as e:
        print(f"❌ Login endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.publicRoute('/confirmSignup', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def confirmSignup():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = Authentication.confirmSignup(data, app)
            return response
    except Exception as e:
        print(f"❌ ConfirmSignup endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.publicRoute('/resetPassword', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def resetPassword():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = Authentication.resetPassword(data, app)
            return response
    except Exception as e:
        print(f"❌ ResetPassword endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.publicRoute('/forgot', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def forgotpassword():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = Authentication.forgotPassword(data, app)
            return response
    except Exception as e:
        print(f"❌ ForgotPassword endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

################################################
## Accounts
################################################
@apiDecorator.protectedRoute('/account', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createAccount():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = AccountSerializer.create(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/types', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def accountTypes():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.getTypes(app)
            return response
    except Exception as e:
        print(f"❌ AccountTypes endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccount(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.get(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/{accountUUID}/users', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccount(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.getUsers(app, AccountID=accountUUID)
            return response
    except Exception as e:
        print(f"❌ GetAccountUsers endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/clients/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getClients(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db

            # Check if optimized version should be used
            query_params = app.current_request.query_params or {}
            use_optimized = query_params.get('optimized', 'true').lower() == 'true'

            if use_optimized:
                from chalicelib.Accounts.optimized_serializer import OptimizedAccountSerializer
                print("🚀 Using optimized clients serializer with server-side sorting")

                # Parse sorting, pagination, and filtering parameters
                sort_by = query_params.get('sort_by')
                sort_desc = query_params.get('sort_desc', 'false').lower() == 'true'

                # Parse page and per_page with error handling
                try:
                    page = int(query_params.get('page', 1))
                except (ValueError, TypeError):
                    page = 1

                try:
                    per_page = int(query_params.get('per_page', 12))
                except (ValueError, TypeError):
                    per_page = 12

                search_query = query_params.get('search')
                status_filter = query_params.get('status_filter')

                # Validate parameters
                page = max(1, page)
                per_page = max(1, min(per_page, 100))  # Limit per_page to reasonable range

                print(f"📄 Request: page={page}, per_page={per_page}, sort_by={sort_by}, sort_desc={sort_desc}")
                print(f"🔍 Filters: search='{search_query}', status='{status_filter}'")

                start_time = time.time()
                response = OptimizedAccountSerializer.getClientsOptimized(
                    accountUUID, app,
                    sort_by=sort_by,
                    sort_desc=sort_desc,
                    page=page,
                    per_page=per_page,
                    search_query=search_query,
                    status_filter=status_filter
                )
                execution_time = time.time() - start_time

                # Add performance metadata
                if response.get('success') and 'metadata' in response:
                    response['metadata']['api_execution_time_seconds'] = execution_time
                    response['metadata']['serializer_used'] = 'optimized'
                    response['metadata']['request_params'] = {
                        'sort_by': sort_by,
                        'sort_desc': sort_desc,
                        'page': page,
                        'per_page': per_page
                    }

                print(f"📊 Clients API completed in {execution_time:.2f} seconds using optimized serializer")
            else:
                print("⚠️ Using legacy clients serializer")
                start_time = time.time()
                response = AccountSerializer.getClients(accountUUID, app)
                execution_time = time.time() - start_time

                # Add performance metadata for comparison
                if response.get('success'):
                    if 'metadata' not in response:
                        response['metadata'] = {}
                    response['metadata']['api_execution_time_seconds'] = execution_time
                    response['metadata']['serializer_used'] = 'legacy'

                print(f"📊 Clients API completed in {execution_time:.2f} seconds using legacy serializer")

            return response
    except Exception as e:
        print(f"❌ GetClients endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/client/status/{accountUUID}', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def toggleStatus(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.toggleClientStatus(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ ToggleStatus endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/accounts', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccounts():
    """EMERGENCY: Per-Lambda session management to prevent zombie connections"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        print("🔄 EMERGENCY: Starting accounts endpoint with per-Lambda session")

        # Use per-Lambda session management (decorators handle auth/logging)
        with quick_session() as db:
            print(f"✅ EMERGENCY: Session created: {type(db)}")

            # Ensure app.current_request exists
            if not hasattr(app, 'current_request') or app.current_request is None:
                print("❌ EMERGENCY: app.current_request is None!")
                raise Exception("app.current_request is not available")

            # Inject session for compatibility
            app.current_request.db = db
            print(f"✅ EMERGENCY: Session injected: {hasattr(app.current_request, 'db')}")

            # Verify session is accessible
            if not hasattr(app.current_request, 'db') or app.current_request.db is None:
                print("❌ EMERGENCY: Session injection failed!")
                raise Exception("Database session injection failed")

            print("🔄 EMERGENCY: Calling AccountSerializer.list...")

            # Call the serializer
            response = AccountSerializer.list(app, CENSOR=False)

            print(f"✅ EMERGENCY: AccountSerializer.list completed successfully")
            return response

    except Exception as e:
        print(f"❌ EMERGENCY: Accounts endpoint error: {e}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        raise e
    finally:
        # Clear the injected session
        try:
            if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
                app.current_request.db = None
                print("🧹 EMERGENCY: Cleared injected session")
        except:
            pass

@apiDecorator.protectedRoute('/accounts/censor', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccounts():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.list(app, CENSOR=True)
            return response
    except Exception as e:
        print(f"❌ GetAccountsCensor endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/{accountUUID}', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def updateAccount(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = AccountSerializer.update(data, accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ UpdateAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/{accountUUID}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def softDeleteAccount(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.delete(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ SoftDeleteAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/{accountUUID}/hard', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteAccount(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.delete(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ DeleteAccountHard endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.get_total_balance(account_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetAccountBalance endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance_history/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalanceDefault(account_uuid):
    """
    Get default (daily) balance history for an account with enhanced error handling.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            print(f"📊 Processing default balance history request for account: {account_uuid}")
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="day")
            return response if response and response.get('success') else {
                'success': False, 'error': 'Invalid response', 'balances': []
            }
    except Exception as e:
        print(f"❌ GetAccountBalanceDefault endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance_history/day/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalanceDay(account_uuid):
    """
    Get daily balance history for an account with enhanced error handling.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            print(f"📊 Processing daily balance history request for account: {account_uuid}")
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="day")
            return response if response and response.get('success') else {
            'success': False, 'error': 'Invalid response', 'balances': []
        }
    except Exception as e:
        print(f"❌ GetAccountBalanceDay endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance_history/month/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalanceMonth(account_uuid):
    """
    Get monthly balance history for an account with enhanced error handling.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            print(f"📊 Processing monthly balance history request for account: {account_uuid}")
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="month")

            # Validate response before returning
            if not response or not isinstance(response, dict):
                print(f"⚠️ Invalid response from AccountSerializer.balance_history")
                return {
                    'success': False,
                    'error': 'Invalid response from balance history service',
                    'balances': []
                }

            print(f"✅ Monthly balance history request completed successfully")
            return response
    except Exception as e:
        print(f"❌ GetAccountBalanceMonth endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance_history/year/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="year")
            return response
    except Exception as e:
        print(f"❌ GetAccountBalanceYear endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/balance_history/six_months/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="six_months")
            return response
    except Exception as e:
        print(f"❌ GetAccountBalanceSixMonths endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/balance_history/week/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="week")
            return response
    except Exception as e:
        print(f"❌ GetAccountBalanceWeek endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance_history/all/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.balance_history(account_uuid, app, GROUP="all")
            return response
    except Exception as e:
        print(f"❌ GetAccountBalanceAll endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/starting_balance/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getStartingBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.starting_balance(account_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetStartingBalance endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/balance/update/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def updateAccountBalance(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.calculateBalance(account_uuid, app)
            return response
    except Exception as e:
        print(f"❌ UpdateAccountBalance endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/account/level_upgrade/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def levelUpgradeRequest(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AccountSerializer.upgradeAccountLevel(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ LevelUpgradeRequest endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None



@apiDecorator.protectedRoute('/subscription/{accountUUID}/create', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def createSubscription(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SubscriptionSerializer.create(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ CreateSubscription endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/subscription/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSubscription(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SubscriptionSerializer.get(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetSubscription endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/subscription/{accountUUID}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteSubscription(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SubscriptionSerializer.delete(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ DeleteSubscription endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

################################################
## Users
################################################
@apiDecorator.protectedRoute('/user', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createUser():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = UsersSerializer.create(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateUser endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/verify', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createUser():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = UsersSerializer.userConfirmed(data, app)
            return response
    except Exception as e:
        print(f"❌ UserVerify endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/users', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getUsers():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.getUsers(app)
            return response
    except Exception as e:
        print(f"❌ GetUsers endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/{userUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getUser(userUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.getUser(userUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetUser endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def updateUser():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = UsersSerializer.update(data, app)
            return response
    except Exception as e:
        print(f"❌ UpdateUser endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/{userUUID}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteUser(userUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.softDelete(userUUID, app)
            return response
    except Exception as e:
        print(f"❌ DeleteUser endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/{userUUID}/hard', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def hardDeleteUser(userUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.delete(userUUID, app)
            return response
    except Exception as e:
        print(f"❌ HardDeleteUser endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/apiKey/{userUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getUserApiKey(userUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.userApiKey(userUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetUserApiKey endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/createCognito/{userUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def createCognito(userUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.createUserCognito(userUUID, app)
            return response
    except Exception as e:
        print(f"❌ CreateCognito endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/user/userLevels', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getUserLevels():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = UsersSerializer.userLevels(app)
            return response
    except Exception as e:
        print(f"❌ GetUserLevels endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None



################################################
## Portfolios
################################################
@apiDecorator.protectedRoute('/account/portfolio/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountPortfolio(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.account_portfolio(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetAccountPortfolio endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolios/symbols', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAllSymbols():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.get_all_symbols_in_portfolios(app)
            return response
    except Exception as e:
        print(f"❌ GetAllSymbols endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/portfolio/{accountUUID}/symbols', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAccountPortfolio(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.get_portfolio(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetAccountPortfolioSymbols endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/portfolio/{accountUUID}/symbols', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def updatePortfolio(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = PortfolioSerializer.update_portfolio(accountUUID, data, app)
            return response
    except Exception as e:
        print(f"❌ UpdatePortfolio endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/portfolio/{accountUUID}/bulk-update', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def bulkUpdatePortfolio(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            # Use the new tracking-enabled bulk update
            response = PortfolioSerializer.bulk_update_with_tracking(accountUUID, data, app, source='portfolio_builder')
            return response
    except Exception as e:
        print(f"❌ BulkUpdatePortfolio endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/portfolio/{accountUUID}/symbols/count', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def countPortfolio(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.count_symbols(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ CountPortfolio endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolio-builder/accounts', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getPortfolioBuilderAccounts():
    """Get all accounts that have used the portfolio builder and are eligible for auto-updates"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.getPortfolioBuilderAccounts(app)
            return response
    except Exception as e:
        print(f"❌ GetPortfolioBuilderAccounts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolio-builder/analytics', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getPortfolioBuilderAnalytics():
    """Get analytics for portfolio builder usage"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            days_back = int(app.current_request.query_params.get('days_back', 30))
            response = PortfolioSerializer.getPortfolioBuilderAnalytics(app, days_back=days_back)
            return response
    except Exception as e:
        print(f"❌ GetPortfolioBuilderAnalytics endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolio-builder/auto-update/{account_id}', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def togglePortfolioAutoUpdate(account_id):
    """Enable or disable auto-updates for a specific account"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            enabled = data.get('enabled', True)
            response = PortfolioSerializer.toggleAutoUpdate(app, int(account_id), enabled=enabled)
            return response
    except Exception as e:
        print(f"❌ TogglePortfolioAutoUpdate endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolio-builder/trigger-updates', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def triggerPortfolioUpdates():
    """Manually trigger portfolio updates from current performance list"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # Get the current performance list
            performance_data = SymbolListSerializer.buildPerformanceList(app)

            if performance_data.get('success'):
                # Trigger portfolio updates
                update_result = SymbolListSerializer.triggerPortfolioUpdates(app, performance_data)
                return update_result
            else:
                return {
                    'success': False,
                    'error': 'Failed to generate performance list for portfolio updates'
                }
    except Exception as e:
        print(f"❌ TriggerPortfolioUpdates endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbol-lists/track-usage', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def trackSymbolListUsage():
    """Track when accounts use symbol lists other than performance list"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            # Extract required parameters
            account_id = data.get('account_id')
            list_type = data.get('list_type')  # e.g., 'sector_list', 'custom_list', 'watchlist'
            symbols_used = data.get('symbols_used', [])

            if not account_id or not list_type or not symbols_used:
                return {
                    'success': False,
                    'error': 'Missing required parameters: account_id, list_type, symbols_used'
                }

            # Track the symbol list usage
            result = SymbolListSerializer.trackSymbolListUsage(app, account_id, list_type, symbols_used)
            return result
    except Exception as e:
        print(f"❌ TrackSymbolListUsage endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolio-builder/active-list/{account_id}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getActivePortfolioList(account_id):
    """Get the currently active portfolio list for an account"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            try:
                account_id = int(account_id)
            except ValueError:
                return {
                    'success': False,
                    'error': 'Invalid account_id format'
                }

            # Get the active portfolio list
            result = PortfolioSerializer.getActivePortfolioList(app, account_id)
            return result
    except Exception as e:
        print(f"❌ GetActivePortfolioList endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/shariah-compliant-performance-list', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getShariahCompliantPerformanceList():
    """Generate Shariah compliant performance list from top performing stocks"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # Get query parameters
            query_params = app.current_request.query_params or {}

            # Extract parameters with defaults
            days_back = int(query_params.get('days_back', 90))
            min_predictions = int(query_params.get('min_predictions', 10))
            min_profit_percentage = float(query_params.get('min_profit_percentage', 0.1))
            max_stocks = int(query_params.get('max_stocks', 50))
            shariah_list_id = int(query_params.get('shariah_list_id', 1))
            account_id = query_params.get('account_id')

            # Convert account_id to int if provided
            if account_id:
                try:
                    account_id = int(account_id)
                except ValueError:
                    return {
                        'success': False,
                        'error': 'Invalid account_id format'
                    }

            # Generate Shariah compliant performance list
            result = SymbolListSerializer.buildShariahCompliantPerformanceList(
                app, days_back, min_predictions, min_profit_percentage, max_stocks, account_id, shariah_list_id
            )
            return result
    except Exception as e:
        print(f"❌ GetShariahCompliantPerformanceList endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/shariah-lists', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getShariahCompliantLists():
    """Get all available Shariah compliant symbol lists"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            result = SymbolListSerializer.getShariahCompliantLists(app)
            return result
    except Exception as e:
        print(f"❌ GetShariahCompliantLists endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/shariah-compliant-performance-list/trigger-updates', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def triggerShariahCompliantPortfolioUpdates():
    """Generate Shariah compliant performance list and trigger portfolio updates"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body or {}

            # Extract parameters with defaults
            days_back = data.get('days_back', 90)
            min_predictions = data.get('min_predictions', 10)
            min_profit_percentage = data.get('min_profit_percentage', 0.1)
            max_stocks = data.get('max_stocks', 50)
            shariah_list_id = data.get('shariah_list_id', 1)

            # Generate Shariah compliant performance list and trigger updates
            result = SymbolListSerializer.buildShariahCompliantPerformanceListWithPortfolioUpdates(
                app, days_back, min_predictions, min_profit_percentage, max_stocks, shariah_list_id
            )
            return result
    except Exception as e:
        print(f"❌ TriggerShariahCompliantPortfolioUpdates endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/shariah-ai-performance-fixture/generate', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def generateShariahAIPerformanceFixture():
    """Generate and update the Shariah AI Performance fixture list"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body or {}

            # Extract parameters with defaults
            days_back = data.get('days_back', 90)
            min_predictions = data.get('min_predictions', 10)
            min_profit_percentage = data.get('min_profit_percentage', 0.1)
            max_stocks = data.get('max_stocks', 100)
            shariah_list_id = data.get('shariah_list_id', 1)

            # Generate Shariah AI Performance fixture
            result = SymbolListSerializer.generateShariahAIPerformanceFixture(
                app, days_back, min_predictions, min_profit_percentage, max_stocks, shariah_list_id
            )
            return result
    except Exception as e:
        print(f"❌ GenerateShariahAIPerformanceFixture endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/admin/reports/profit-loss', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAdminProfitLossReports():
    """Admin reports endpoint for profit/loss analysis across all accounts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # Get query parameters
            query_params = app.current_request.query_params or {}

            # SECURITY: Validate account_owner_id access permissions
            requested_account_owner_id = query_params.get('account_owner_id')
            if requested_account_owner_id:
                try:
                    requested_account_owner_id = int(requested_account_owner_id)
                except (ValueError, TypeError):
                    return {"success": False, "message": "Invalid account_owner_id format"}

            # SECURITY: Determine allowed account_owner_id based on user permissions
            request_user = getattr(app.current_request, 'requestUser', None)
            if not request_user:
                return {"success": False, "message": "Authentication required"}

            user_level_id = getattr(request_user.userLevel, 'id', None) if request_user.userLevel else None

            # Define access control rules
            allowed_account_owner_ids = []
            if user_level_id == 1:  # Super Admin - can see all
                allowed_account_owner_ids = [1, 77]  # Thriving and Innova
            elif user_level_id == 2:  # Innova Admin - can only see Innova (77)
                allowed_account_owner_ids = [77]  # Innova only
            elif user_level_id == 3:  # Thriving Admin - can only see Thriving (1)
                allowed_account_owner_ids = [1]  # Thriving only
            else:
                return {"success": False, "message": "Insufficient permissions to access reports"}

            # SECURITY: Validate requested account_owner_id against allowed IDs
            if requested_account_owner_id and requested_account_owner_id not in allowed_account_owner_ids:
                print(f"🚨 SECURITY VIOLATION: User level {user_level_id} attempted to access account_owner_id {requested_account_owner_id}")
                return {"success": False, "message": "Access denied: You don't have permission to view these accounts"}

            # If no account_owner_id specified, default to user's primary access
            if not requested_account_owner_id:
                if user_level_id == 2:  # Innova Admin
                    requested_account_owner_id = 77  # Default to Innova
                elif user_level_id == 3:  # Thriving Admin
                    requested_account_owner_id = 1   # Default to Thriving
                elif user_level_id == 1:  # Super Admin
                    requested_account_owner_id = 77  # Default to Innova for now
                else:
                    return {"success": False, "message": "Unable to determine account access"}

            print(f"🔐 User level {user_level_id} accessing account_owner_id {requested_account_owner_id}")

            # Check if optimized version should be used
            use_optimized = query_params.get('optimized', 'true').lower() == 'true'

            if use_optimized:
                from chalicelib.Reports.optimized_serializer import OptimizedReportsSerializer
                serializer_class = OptimizedReportsSerializer
                print("🚀 Using optimized reports serializer")
            else:
                from chalicelib.Reports.serializer import ReportsSerializer
                serializer_class = ReportsSerializer
                print("⚠️ Using legacy reports serializer")

            # Get other query parameters
            interval = query_params.get('interval', '1_day')
            days = query_params.get('days')
            show_excluded_accounts = query_params.get('show_excluded_accounts')

            if days:
                try:
                    days = int(days)
                except (ValueError, TypeError):
                    days = None

            show_excluded_accounts = show_excluded_accounts == 'true' if show_excluded_accounts else None

            # Track performance
            start_time = time.time()
            result = serializer_class.getProfitLossReports(app, interval, days, requested_account_owner_id, show_excluded_accounts)
            execution_time = time.time() - start_time

            # Add performance metadata
            if result.get('success') and 'metadata' in result:
                result['metadata']['api_execution_time_seconds'] = execution_time
                result['metadata']['serializer_used'] = 'optimized' if use_optimized else 'legacy'
                result['metadata']['user_level_id'] = user_level_id
                result['metadata']['account_owner_id'] = requested_account_owner_id

                print(f"📊 Reports API completed in {execution_time:.2f} seconds using {'optimized' if use_optimized else 'legacy'} serializer")
                return result
    except Exception as e:
        print(f"❌ GetAdminProfitLossReports endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/admin/reports/test', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testAdminReportsAuth():
    """Test endpoint to verify admin authentication and permissions"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            request_user = getattr(app.current_request, 'requestUser', None)
            if not request_user:
                return {"success": False, "message": "No requestUser found"}

            user_level_id = getattr(request_user.userLevel, 'id', None) if request_user.userLevel else None
            user_info = {
                "user_level_id": user_level_id,
                "has_user_level": hasattr(request_user, 'userLevel'),
                "user_level_object": str(request_user.userLevel) if request_user.userLevel else None,
                "request_user_attrs": [attr for attr in dir(request_user) if not attr.startswith('_')]
            }

            return {
                "success": True,
                "message": "Authentication test successful",
                "user_info": user_info
            }
    except Exception as e:
        print(f"❌ TestAdminReportsAuth endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########           Transfer API Endpoints   ###################
#################################################################

@apiDecorator.protectedRoute('/admin/transfers', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getTransfers():
    """
    Get transfers for an account or all accounts.

    Query Parameters:
    - account_id: Filter by specific account ID (optional)
    - days_back: Number of days to look back (default: 30)
    - transfer_type: Filter by transfer type (deposit, withdrawal, dividend, etc.)
    - brokerage_type: Filter by brokerage (alpaca, robinhood, etrade, schwab)
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.transferSerializer import TransferSerializer
            from chalicelib.Bots.models import Transfers
            from chalicelib.Utils.functions import unixTimeNow

            # Get query parameters
            query_params = app.current_request.query_params or {}
            account_id = query_params.get('account_id')
            days_back = int(query_params.get('days_back', 30))
            transfer_type = query_params.get('transfer_type')
            brokerage_type = query_params.get('brokerage_type')

            cutoff_time = unixTimeNow() - (days_back * 24 * 60 * 60)

            # Build query
            query = db.query(Transfers).filter(Transfers.created_at >= cutoff_time)

            if account_id:
                query = query.filter(Transfers.account_id == int(account_id))

            if transfer_type:
                query = query.filter(Transfers.transfer_type == transfer_type)

            if brokerage_type:
                query = query.filter(Transfers.brokerage_type == brokerage_type)

            transfers = query.order_by(Transfers.transfer_date.desc()).all()

            # Format response
            transfer_data = []
            for transfer in transfers:
                transfer_data.append({
                    'id': transfer.id,
                    'uuid': transfer.uuid,
                    'account_id': transfer.account_id,
                    'trading_bot_id': transfer.trading_bot_id,
                    'direction': 'inbound' if transfer.direction == 1 else 'outbound',
                    'amount': float(transfer.amount),
                    'transfer_type': transfer.transfer_type,
                    'brokerage_type': transfer.brokerage_type,
                    'status': transfer.status,
                    'description': transfer.description,
                    'transfer_date': transfer.transfer_date,
                    'settlement_date': transfer.settlement_date,
                    'created_at': transfer.created_at
                })

            return {
                'success': True,
                'transfers': transfer_data,
                'total_count': len(transfer_data)
            }
    except Exception as e:
        print(f"❌ GetTransfers endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/transfers/update', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def updateTransfersManual():
    """
    Manually trigger transfer updates for all or specific trading bots.

    Body Parameters:
    - bot_uuid: Update transfers for specific bot (optional)
    - force_update: Force update even if recently updated (optional)
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.transferSerializer import TransferSerializer
            from chalicelib.Bots.models import TradingBot

            request_json = app.current_request.json_body or {}
            bot_uuid = request_json.get('bot_uuid')

            if bot_uuid:
                # Update specific bot
                bot = db.query(TradingBot).filter(TradingBot.uuid == bot_uuid).first()

                if not bot:
                    return {
                        'success': False,
                        'error': f'Trading bot {bot_uuid} not found'
                    }

                # Update transfers for specific bot
                if bot.bot_type_id == 1:  # E-Trade
                    TransferSerializer.update_etrade_transfers(bot, app)
                elif bot.bot_type_id == 2:  # Schwab
                    TransferSerializer.update_schwab_transfers(bot, app)
                elif bot.bot_type_id == 3:  # Robinhood
                    TransferSerializer.update_robinhood_transfers(bot, app)
                elif bot.bot_type_id == 6:  # Alpaca
                    TransferSerializer.update_alpaca_transfers(bot, app)
                else:
                    return {
                        'success': False,
                        'error': f'Unsupported bot type {bot.bot_type_id}'
                    }

                return {
                    'success': True,
                    'message': f'Transfers updated for bot {bot_uuid}'
                }
            else:
                # Update all bots
                results = TransferSerializer.update_all_transfers(app)

                return {
                    'success': True,
                    'results': results
                }
    except Exception as e:
        print(f"❌ UpdateTransfersManual endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/transfers/summary', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getTransfersSummary():
    """
    Get transfer summary statistics.

    Query Parameters:
    - account_id: Filter by specific account ID (optional)
    - days_back: Number of days to look back (default: 30)
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.models import Transfers
            from chalicelib.Utils.functions import unixTimeNow
            from sqlalchemy import func

            # Get query parameters
            query_params = app.current_request.query_params or {}
            account_id = query_params.get('account_id')
            days_back = int(query_params.get('days_back', 30))

            cutoff_time = unixTimeNow() - (days_back * 24 * 60 * 60)

            # Build base query
            query = db.query(Transfers).filter(Transfers.created_at >= cutoff_time)

            if account_id:
                query = query.filter(Transfers.account_id == int(account_id))

            # Get summary statistics
            total_inbound = query.filter(Transfers.direction == 1).with_entities(
                func.sum(Transfers.amount)
            ).scalar() or 0

            total_outbound = query.filter(Transfers.direction == 2).with_entities(
                func.sum(Transfers.amount)
            ).scalar() or 0

            # Get counts by type
            transfer_counts = db.query(
                Transfers.transfer_type,
                func.count(Transfers.id).label('count'),
                func.sum(Transfers.amount).label('total_amount')
            ).filter(
                Transfers.created_at >= cutoff_time
            )

            if account_id:
                transfer_counts = transfer_counts.filter(Transfers.account_id == int(account_id))

            transfer_counts = transfer_counts.group_by(Transfers.transfer_type).all()

            # Format counts
            counts_by_type = {}
            for transfer_type, count, total_amount in transfer_counts:
                counts_by_type[transfer_type or 'unknown'] = {
                    'count': count,
                    'total_amount': float(total_amount or 0)
                }

            return {
                'success': True,
                'summary': {
                    'total_inbound': float(total_inbound),
                    'total_outbound': float(total_outbound),
                    'net_transfer': float(total_inbound - total_outbound),
                    'counts_by_type': counts_by_type,
                    'days_back': days_back
                }
            }
    except Exception as e:
        print(f"❌ GetTransfersSummary endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/transfers/detect-retroactive', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def detectRetroactiveTransfers():
    """
    Detect and create transfer records for historical balance changes.

    This endpoint analyzes historical account balance data to identify potential
    transfers based on significant daily balance changes (default: 10% or more).

    Body Parameters:
    - account_uuid: Specific account UUID to analyze (optional, analyzes all if not provided)
    - threshold_percentage: Minimum percentage change to consider as transfer (default: 10.0)
    - dry_run: If true, only analyze without creating transfer records (default: false)

    Example Request:
    {
        "account_uuid": "********-1234-1234-1234-********9012",
        "threshold_percentage": 15.0,
        "dry_run": false
    }
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.transferSerializer import TransferSerializer
            from chalicelib.Accounts.models import Account

            request_json = app.current_request.json_body or {}
            account_uuid = request_json.get('account_uuid')
            threshold_percentage = float(request_json.get('threshold_percentage', 10.0))
            dry_run = request_json.get('dry_run', False)

            # Validate threshold
            if threshold_percentage < 1.0 or threshold_percentage > 100.0:
                return {
                    'success': False,
                    'error': 'threshold_percentage must be between 1.0 and 100.0'
                }

            # Get account ID if account_uuid is provided
            account_id = None
            if account_uuid:
                try:
                    account = db.query(Account).filter(Account.uuid == account_uuid).one()
                    account_id = account.id
                except:
                    return {
                        'success': False,
                        'error': f'Account {account_uuid} not found'
                    }

            print(f"🔍 Starting retroactive transfer detection...")
            print(f"   📊 Account: {'All accounts' if not account_uuid else account_uuid}")
            print(f"   📈 Threshold: {threshold_percentage}%")
            print(f"   🧪 Dry run: {dry_run}")

            if dry_run:
                # For dry run, we'll analyze but not create transfers
                print("🧪 Running in dry-run mode - no transfers will be created")

            # Run the detection
            results = TransferSerializer.detect_retroactive_transfers(
                account_id=account_id,
                threshold_percentage=threshold_percentage,
                dry_run=dry_run,
                app=app
            )

            # Add request parameters to results
            results['request_parameters'] = {
                'account_uuid': account_uuid,
                'threshold_percentage': threshold_percentage,
                'dry_run': dry_run
            }

            return {
                'success': True,
                'message': f'Retroactive transfer detection completed. Found {results["potential_transfers_detected"]} potential transfers, created {results["transfers_created"]} new records.',
                'results': results
            }

    except ValueError as e:
        return {
            'success': False,
            'error': f'Invalid parameter: {str(e)}'
        }
    except Exception as e:
        print(f"❌ DetectRetroactiveTransfers endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/transfers/cleanup-duplicates', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def cleanupDuplicateTransfers():
    """
    Clean up duplicate transfers that were created for initial account funding.

    This endpoint identifies and fixes cases where:
    1. The starting balance was set to the first deposit amount
    2. A transfer record was also created for that same amount
    3. This results in double-counting the initial funding

    Body Parameters:
    - account_uuid: Specific account UUID to clean up (optional, cleans all if not provided)
    - dry_run: If true, only analyze without making changes (default: true)

    Example Request:
    {
        "account_uuid": "********-1234-1234-1234-********9012",
        "dry_run": false
    }
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.transferSerializer import TransferSerializer
            from chalicelib.Accounts.models import Account

            # Get request parameters
            data = app.current_request.json_body or {}
            account_uuid = data.get('account_uuid')
            dry_run = data.get('dry_run', True)  # Default to dry run for safety

            # Convert account UUID to account ID if provided
            account_id = None
            if account_uuid:
                account = db.query(Account).filter(Account.uuid == account_uuid).first()
                if not account:
                    return {
                        'success': False,
                        'error': f'Account not found: {account_uuid}'
                    }
                account_id = account.id

            # Run the cleanup
            results = TransferSerializer.cleanup_duplicate_initial_transfers(
                account_id=account_id,
                dry_run=dry_run,
                app=app
            )

            return {
                'success': True,
                'results': results,
                'dry_run': dry_run
            }

    except Exception as e:
        print(f"❌ CleanupDuplicateTransfers endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/portfolio/types', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getPortfolioTypes():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.get_types(app)
            return response
    except Exception as e:
        print(f"❌ GetPortfolioTypes endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/cap/types', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getCapTypes():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.get_cap_market_types(app)
            return response
    except Exception as e:
        print(f"❌ GetCapTypes endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/portfolio/{accountUUID}/update', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def updateAccountPortfolio(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = PortfolioSerializer.update_account_portfolio(accountUUID, data, app)
            return response
    except Exception as e:
        print(f"❌ UpdateAccountPortfolio endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/portfolio/duplicates/report', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getPortfolioDuplicatesReport():
    """Get a report of duplicate symbols in portfolios"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            account_id = app.current_request.query_params.get('account_id')
            if account_id:
                account_id = int(account_id)
            response = PortfolioSerializer.get_duplicate_symbols_report(app, account_id)
            return response
    except Exception as e:
        print(f"❌ GetPortfolioDuplicatesReport endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/portfolio/duplicates/cleanup', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def cleanupPortfolioDuplicates():
    """Clean up duplicate symbols in portfolios"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body or {}
            account_id = data.get('account_id')
            if account_id:
                account_id = int(account_id)
            response = PortfolioSerializer.cleanup_duplicate_symbols(app, account_id)
            return response
    except Exception as e:
        print(f"❌ CleanupPortfolioDuplicates endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/account/portfolio/showcase', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def showcasePortfolio():
    body = app.current_request.json_body
    if os.environ['STAGE'] != 'local':
        payload = {
            "multiValueQueryStringParameters": None,
            "headers": {
                "x-api-key": os.environ['AI_API_KEY'],
                "access-token": None,
                "refresh-token": None,
                "account-id": None
            },
            "pathParameters": None,
            "requestContext": {
                "httpMethod": None,
                "resourcePath": None
            },
            "body": None,
            "stageVariables": {
                "stage": os.environ['STAGE']
            }
        }
        payload['body'] = json.dumps(body)

        ## Call the lambda
        lambdaName = os.environ['BUILD_SHOWCASE_LAMBDA']
        lambda_response = boto3.client('lambda').invoke(
            FunctionName=lambdaName,
            InvocationType='Event',
            Payload=json.dumps(payload)
        )
        return {"success": True}
    else:
        response = PortfolioSerializer.generatePortfolioShowcase(body, app)
        return response


@app.lambda_function(name='build_showcase')
def ProcessBuildShowcase(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import json

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            result = PortfolioSerializer.generatePortfolioShowcase(body, app)
            return result
    except Exception as e:
        print(f"❌ ProcessBuildShowcase lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

################################################
## Trading Automations
################################################
@apiDecorator.protectedRoute('/bot/create', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createBot():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = BotSerializer.create(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateBot endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/bot/update', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def updateBot():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = BotSerializer.update(data, app)
            return response
    except Exception as e:
        print(f"❌ UpdateBot endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/bot/authorize', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def authBot():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = BotSerializer.authorizeBot(app, DATA=data)
            return response
    except Exception as e:
        print(f"❌ AuthBot endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/delete/{bot_uuid}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteAutomation(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.delete(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ DeleteAutomation endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/cleanup', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def cleanupIncompleteAutomations():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.cleanup_incomplete_automations(app)
            return response
    except Exception as e:
        print(f"❌ CleanupIncompleteAutomations endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/balance/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAutomationBalance(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.get_balance(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetAutomationBalance endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.publicRoute('/callback/etrade', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def etradeCallback():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.authorizeBot(app, BOT_TYPE_ID=1)
            return response
    except Exception as e:
        print(f"❌ EtradeCallback endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automation/renew_token/{botUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def refreshToken(botUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.renewBotToken(botUUID, app)
            return response
    except Exception as e:
        print(f"❌ RefreshToken endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.publicRoute('/callback/schwab', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def schwabCallback():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.authorizeBot(app, BOT_TYPE_ID=2)
            return response
    except Exception as e:
        print(f"❌ SchwabCallback endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automation/access_token/{bot_uuid}/renew', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def renewToken(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.renewBotToken(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ RenewToken endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def accountAutomations(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.get(account_uuid, app)
            return response
    except Exception as e:
        print(f"❌ AccountAutomations endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/disable/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def automationDisable(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.disable(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ AutomationDisable endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/enable/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def automationEnable(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.enable(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ AutomationEnable endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/brokerage_accounts/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAutomationAccounts(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.get_accounts(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetAutomationAccounts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/assign_account/{bot_uuid}', content_types=['application/json'], methods=['PATCH'], cors=corsConfig)
def assignAutomationAccount(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = BotSerializer.assign_brokerage_account(bot_uuid, data, app)
            return response
    except Exception as e:
        print(f"❌ AssignAutomationAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/otp/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAutomationOTP(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.otp(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetAutomationOTP endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/trades/order/status/{trade_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getTradeOrderStatus(trade_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = BotSerializer.update_order_status(trade_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetTradeOrderStatus endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/orders/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getOrders(account_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.get_orders(account_uuid, app)
            return response
    except Exception as e:
        print(f"❌ GetOrders endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automation/import/orders/stocks/{automation_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def importOrders(automation_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.import_stock_orders(automation_uuid, app)
            return response
    except Exception as e:
        print(f"❌ ImportOrders endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automation/performance/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def automationPerformance(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.automation_performance(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ AutomationPerformance endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automation/positions/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def automationPositions(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.get_positions(bot_uuid, app)
            return response
    except Exception as e:
        print(f"❌ AutomationPositions endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/ai/portfolio/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def portfolioAIChoices(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.portfolioProcess(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ PortfolioAIChoices endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/process-all-active-portfolios', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def processAllActivePortfolios():
    """Process portfolios for all accounts with active automation (asynchronously)"""
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Accounts.models import Account
    import time
    import concurrent.futures
    import threading
    from chalicelib.Utils.database import get_db_session

    start_time = time.time()
    processed_accounts = []
    failed_accounts = []

    def process_single_account(account_data):
        """Process a single account's portfolio in a separate thread"""
        account_uuid, account_info = account_data
        thread_result = {
            'account_uuid': account_uuid,
            'account_name': account_info['account_name'],
            'account_id': account_info['account_id'],
            'automations_count': len(account_info['automations']),
            'automations': account_info['automations']
        }

        try:
            print(f"🔄 [Thread] Processing portfolio for account: {account_info['account_name']} ({account_uuid})")

            # Create a new app context for this thread with its own database session
            from chalicelib.Utils.functions import ChaliceRequest
            thread_app = type('ThreadApp', (), {})()
            thread_app.current_request = ChaliceRequest({
                'headers': app.current_request.headers,
                'pathParameters': {},
                'queryStringParameters': {},
                'body': None
            })

            # Use ultra-safe session for thread safety with retry logic
            max_retries = 2
            for attempt in range(max_retries + 1):
                try:
                    with get_db_session() as thread_db:
                        thread_app.current_request.db = thread_db

                        # Call the existing portfolioProcess method
                        result = AutomationSerializer.portfolioProcess(account_uuid, thread_app, DAYTRADE=False)

                        thread_result.update({
                            'result': result,
                            'status': 'success'
                        })

                        print(f"✅ [Thread] Successfully processed portfolio for {account_info['account_name']}")
                        return thread_result

                except Exception as db_error:
                    error_msg = str(db_error).lower()
                    if ("lost connection" in error_msg or "timed out" in error_msg) and attempt < max_retries:
                        print(f"⚠️  [Thread] Database connection issue for {account_info['account_name']}, retrying... (attempt {attempt + 1}/{max_retries + 1})")
                        time.sleep(1)  # Brief delay before retry
                        continue
                    else:
                        # Final attempt failed or non-connection error
                        raise db_error

        except Exception as e:
            error_msg = str(e)
            print(f"❌ [Thread] Failed to process portfolio for {account_info['account_name']}: {error_msg}")

            thread_result.update({
                'error': error_msg,
                'status': 'failed'
            })
            return thread_result

    try:
        # Initialize database connection if not already present
        if not hasattr(app.current_request, 'db') or app.current_request.db is None:
            from chalicelib.Utils.database import conn
            app.current_request.db = conn()

        db = app.current_request.db

        # Get all accounts with active automations
        active_automations = db.query(TradingBot).join(
            Account, TradingBot.account_id == Account.id
        ).filter(
            TradingBot.status_id == 1,  # Enabled
            TradingBot.brokerage_connected == True,  # Connected to brokerage
            Account.status_id.in_([1, 2, 3, 7])  # Active account statuses
        ).all()

        if not active_automations:
            return {
                'success': True,
                'message': 'No accounts with active automation found',
                'processed_accounts': 0,
                'failed_accounts': 0,
                'execution_time': round(time.time() - start_time, 2)
            }

        # Group by account to avoid processing the same account multiple times
        unique_accounts = {}
        for automation in active_automations:
            account_uuid = automation.account.uuid
            if account_uuid not in unique_accounts:
                unique_accounts[account_uuid] = {
                    'account_id': automation.account.id,
                    'account_name': automation.account.name,
                    'automations': []
                }
            unique_accounts[account_uuid]['automations'].append({
                'bot_id': automation.id,
                'bot_uuid': automation.uuid,
                'bot_type': automation.trading_bot_type.name if automation.trading_bot_type else 'Unknown'
            })

        print(f"🔍 Found {len(unique_accounts)} unique accounts with active automation")
        print(f"🚀 Starting asynchronous processing with max {min(len(unique_accounts), 3)} concurrent threads")

        # Process accounts asynchronously using ThreadPoolExecutor with reduced concurrency
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(unique_accounts), 3)) as executor:
            # Submit all account processing tasks
            future_to_account = {
                executor.submit(process_single_account, account_data): account_data[0]
                for account_data in unique_accounts.items()
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_account):
                account_uuid = future_to_account[future]
                try:
                    result = future.result()
                    if result['status'] == 'success':
                        processed_accounts.append(result)
                    else:
                        failed_accounts.append(result)
                except Exception as e:
                    print(f"❌ [Main] Exception in thread for account {account_uuid}: {str(e)}")
                    failed_accounts.append({
                        'account_uuid': account_uuid,
                        'error': f"Thread execution failed: {str(e)}",
                        'status': 'failed'
                    })

        execution_time = round(time.time() - start_time, 2)

        return {
            'success': True,
            'message': f'Asynchronous portfolio processing completed for {len(processed_accounts)} accounts',
            'processed_accounts': len(processed_accounts),
            'failed_accounts': len(failed_accounts),
            'total_accounts_found': len(unique_accounts),
            'execution_time': execution_time,
            'processing_method': 'asynchronous',
            'max_concurrent_threads': min(len(unique_accounts), 10),
            'results': {
                'successful': processed_accounts,
                'failed': failed_accounts
            }
        }

    except Exception as e:
        execution_time = round(time.time() - start_time, 2)
        print(f"❌ Error in processAllActivePortfolios: {str(e)}")

        # Emergency cleanup on error
        from chalicelib.Utils.lambda_session import emergency_cleanup
        emergency_cleanup()

        return {
            'success': False,
            'error': str(e),
            'processed_accounts': len(processed_accounts),
            'failed_accounts': len(failed_accounts),
            'execution_time': execution_time,
            'processing_method': 'asynchronous',
            'results': {
                'successful': processed_accounts,
                'failed': failed_accounts
            }
        }
    finally:
        # Clean up database connection
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/automations/update_profits/{botUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def updateProfits(botUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.updateProfitCalculations(botUUID, app)
            return response
    except Exception as e:
        print(f"❌ UpdateProfits endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

#################################################################
###########             Stock Selection       ###################
#################################################################
@apiDecorator.protectedRoute('/symbol-lists', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSymbolList():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SymbolListSerializer.getLists(app)
            return response
    except Exception as e:
        print(f"❌ GetSymbolList endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbol-list/{listUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSymbolList(listUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SymbolListSerializer.get(listUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetSymbolListByUUID endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbol-list/create', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createSymbolList():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = SymbolListSerializer.create(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateSymbolList endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbol-list/update', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createSymbolList():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = SymbolListSerializer.create(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateSymbolListUpdate endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbol-list/{listUUID}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteSymbolList(listUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SymbolListSerializer.delete(listUUID, app)
            return response
    except Exception as e:
        print(f"❌ DeleteSymbolList endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/update', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def createSymbolList():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SymbolSerializer._updateSymbols(app)
            return response
    except Exception as e:
        print(f"❌ UpdateSymbols endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/sectors', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSectors():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SymbolListSerializer.getSectors(app)
            return response
    except Exception as e:
        print(f"❌ GetSectors endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSymbols():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SymbolSerializer.getList(app)
            return response
    except Exception as e:
        print(f"❌ GetSymbols endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/performance-list', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getPerformanceList():
    """
    Get top 100 stocks ranked by prediction accuracy.

    Automatically filters out:
    - Micro Cap and Nano Cap stocks (higher risk/volatility)
    - Stocks with insufficient profit percentage
    - Stocks with too few predictions

    Query Parameters:
    - days_back: Number of days to analyze (default: 90)
    - min_predictions: Minimum predictions required (default: 10)
    - min_profit_percentage: Minimum average profit percentage required (default: 0.1)
    - account_id: Account ID for tracking usage (optional)

    Example: /symbols/performance-list?days_back=60&min_predictions=15&min_profit_percentage=0.5&account_id=123
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # Get query parameters with defaults
            days_back = int(app.current_request.query_params.get('days_back', 30)) if app.current_request.query_params else 90
            min_predictions = int(app.current_request.query_params.get('min_predictions', 10)) if app.current_request.query_params else 10
            min_profit_percentage = float(app.current_request.query_params.get('min_profit_percentage', 0.1)) if app.current_request.query_params else 0.1
            account_id = app.current_request.query_params.get('account_id') if app.current_request.query_params else None

            # Build the performance list with tracking
            response = SymbolListSerializer.buildPerformanceList(app, days_back=days_back, min_predictions=min_predictions, min_profit_percentage=min_profit_percentage, account_id=account_id)
            return response
    except Exception as e:
        print(f"❌ GetPerformanceList endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/generate-performance-fixture', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def generatePerformanceFixture():
    """
    Generate a fixture entry for the AI Performance Top 100 list.
    This creates a symbol list based on prediction accuracy that can be used as a fixture.

    Query Parameters:
    - days_back: Number of days to analyze (default: 90)
    - min_predictions: Minimum predictions required (default: 10)
    - min_profit_percentage: Minimum average profit percentage required (default: 0.1)

    Returns the fixture entry that can be added to symbol_lists.py
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # Get query parameters with defaults
            days_back = int(app.current_request.query_params.get('days_back', 90)) if app.current_request.query_params else 90
            min_predictions = int(app.current_request.query_params.get('min_predictions', 10)) if app.current_request.query_params else 10
            min_profit_percentage = float(app.current_request.query_params.get('min_profit_percentage', 0.1)) if app.current_request.query_params else 0.1

            fixture_entry = SymbolSerializer.generatePerformanceFixture(app, days_back=days_back, min_predictions=min_predictions, min_profit_percentage=min_profit_percentage)

            if fixture_entry:
                return {
                    "success": True,
                    "message": "Performance fixture generated successfully",
                    "fixture_entry": fixture_entry,
                    "instructions": "Copy the fixture_entry to api/chalicelib/SymbolLists/Fixtures/symbol_lists.py to replace the existing AI Performance Top 100 entry"
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to generate performance fixture - insufficient data",
                    "fixture_entry": None
                }
    except Exception as e:
        print(f"❌ GeneratePerformanceFixture endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

#################################################################
###########        Performance Fixture Cron   ###################
#################################################################
@app.schedule(Cron('0', '2', '1', '*', '?', '*'))  # Run at 2:00 AM on the 1st day of every month
def updatePerformanceFixture(event):
    """
    Automated cron job to update the AI Performance Top 100 fixture every 30 days.
    Runs on the 1st day of each month at 2:00 AM UTC.

    This job:
    1. Generates a new performance fixture based on the last 90 days of data
    2. Applies standard filtering (min 10 predictions, 0.1% profit threshold)
    3. Writes the updated fixture to the symbol_lists.py file
    4. Logs the results for monitoring
    """
    import os
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from datetime import datetime

    try:
        with quick_session() as db:
            # Set up the app context
            app.current_request = ChaliceRequest({})
            app.current_request.db = db

        print(f"[{datetime.now()}] Starting monthly performance fixture update with portfolio auto-updates...")

        # Generate the new performance fixture with portfolio updates
        result = SymbolListSerializer.generatePerformanceFixtureWithPortfolioUpdates(
            app,
            days_back=90,           # Analyze last 90 days
            min_predictions=10,     # Minimum 10 predictions required
            min_profit_percentage=0.1  # Minimum 0.1% average profit
        )

        fixture_entry = result.get('fixture_entry') if result.get('success') else None

        if fixture_entry and fixture_entry.get('symbols'):
            # Update the fixture file
            fixture_file_path = '/opt/chalice/chalicelib/SymbolLists/Fixtures/symbol_lists.py'

            # Read the current fixture file
            with open(fixture_file_path, 'r') as file:
                content = file.read()

            # Find and replace the AI Performance Top 100 fixture
            # Look for the fixture with id: 7
            import re

            # Pattern to match the AI Performance Top 100 fixture
            pattern = r'(\s+{\s+"id":\s+7,\s+"name":\s+"AI Performance Top 100",.*?"symbols":\s+\[)[^]]*(\]\s+})'

            # Create the new symbols list string
            symbols_str = ',\n            '.join([f'"{symbol}"' for symbol in fixture_entry['symbols']])
            replacement = f'\\g<1>\n            {symbols_str}\n        \\g<2>'

            # Replace the fixture
            updated_content = re.sub(pattern, replacement, content, flags=re.DOTALL)

            # Write the updated content back to the file
            with open(fixture_file_path, 'w') as file:
                file.write(updated_content)

            print(f"[{datetime.now()}] Successfully updated AI Performance Top 100 fixture with {len(fixture_entry['symbols'])} symbols")
            print(f"[{datetime.now()}] Top 10 symbols: {fixture_entry['symbols'][:10]}")

            # Log portfolio update results
            if result.get('portfolio_updates'):
                portfolio_result = result['portfolio_updates'].get('portfolio_update_result', {})
                print(f"[{datetime.now()}] Portfolio updates: {portfolio_result.get('total_updated', 0)} accounts updated, {portfolio_result.get('total_failed', 0)} failed")

                if portfolio_result.get('updated_accounts'):
                    print(f"[{datetime.now()}] Updated account IDs: {[acc['account_id'] for acc in portfolio_result['updated_accounts']]}")

                # Log success metrics
                print(f"[{datetime.now()}] Fixture update and portfolio auto-updates completed successfully")

        else:
            print(f"[{datetime.now()}] Failed to generate performance fixture - insufficient data")
    except Exception as e:
        print(f"❌ UpdatePerformanceFixture cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########     Social Media Token Refresh Cron   ##############
#################################################################
@app.schedule(Cron('0', '2', '?', '*', '*', '*'))  # Run daily at 2:00 AM UTC
def refreshSocialMediaTokens(event):
    """
    Automated cron job to refresh expiring social media tokens.
    Runs daily at 2:00 AM UTC to check for tokens expiring within 7 days and refresh them.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.models import SocialMediaAccount
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer
    from chalicelib.Utils.functions import unixTimeNow, ChaliceRequest

    print("🔄 Starting social media token refresh check...")

    try:
        with quick_session() as db:
            # Create mock app context for the serializer
            context = {
                "requestContext": {
                    "requestId": "cron-social-media-refresh",
                    "stage": os.environ.get('STAGE', 'local')
                },
                "headers": {
                    "x-api-key": "cron-job"
                },
                "query_params": {}
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            # Calculate timestamp for 7 days from now
            seven_days_from_now = unixTimeNow() + (7 * 24 * 60 * 60)

            # Find accounts with tokens expiring within 7 days
            expiring_accounts = db.query(SocialMediaAccount).filter(
                SocialMediaAccount.token_expires_at.isnot(None),
                SocialMediaAccount.token_expires_at <= seven_days_from_now,
                SocialMediaAccount.is_active == True,
                SocialMediaAccount.connection_status == 'connected'
            ).all()

            print(f"📊 Found {len(expiring_accounts)} accounts with tokens expiring within 7 days")

            refresh_results = {
                'total_checked': len(expiring_accounts),
                'successful_refreshes': 0,
                'failed_refreshes': 0,
                'errors': []
            }

            for account in expiring_accounts:
                try:
                    days_until_expiry = (account.token_expires_at - unixTimeNow()) // 86400
                    print(f"🔄 Refreshing token for {account.platform} account (expires in {days_until_expiry} days)")

                    # Use the existing refresh_token method
                    # Note: This requires super user validation, so we'll call the internal logic directly
                    result = SocialMediaSerializer._refresh_account_token(account, db)

                    if result['success']:
                        refresh_results['successful_refreshes'] += 1
                        print(f"✅ Successfully refreshed {account.platform} token")
                    else:
                        refresh_results['failed_refreshes'] += 1
                        error_msg = f"Failed to refresh {account.platform} token: {result.get('error', 'Unknown error')}"
                        refresh_results['errors'].append(error_msg)
                        print(f"❌ {error_msg}")

                except Exception as e:
                    refresh_results['failed_refreshes'] += 1
                    error_msg = f"Exception refreshing {account.platform} token: {str(e)}"
                    refresh_results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")

            print(f"📊 Token refresh summary:")
            print(f"   Total checked: {refresh_results['total_checked']}")
            print(f"   Successful: {refresh_results['successful_refreshes']}")
            print(f"   Failed: {refresh_results['failed_refreshes']}")

            if refresh_results['errors']:
                print(f"   Errors: {refresh_results['errors']}")

    except Exception as e:
        print(f"❌ Social media token refresh cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########     Shariah Performance Fixture Cron   #############
#################################################################
@app.schedule(Cron('0', '3', '1', '*', '?', '*'))  # Run at 3:00 AM on the 1st day of every month
def updateShariahPerformanceFixture(event):
    """
    Automated cron job to update the Shariah AI Performance Top 100 fixture every 30 days.
    Runs on the 1st day of each month at 3:00 AM UTC (1 hour after regular AI Performance update).

    This cron job:
    1. Generates a new Shariah compliant performance list from top performers
    2. Updates the Shariah AI Performance Top 100 fixture (ID: 8) in the database
    3. Triggers portfolio updates for accounts using Shariah compliant portfolios
    4. Maintains Shariah compliance while optimizing performance
    """
    import os
    from chalicelib.SymbolLists.serializer import SymbolListSerializer
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from datetime import datetime
    import traceback

    try:
        with quick_session() as db:
            # Set up the app context
            app.current_request = ChaliceRequest({})
            app.current_request.db = db

            print(f"[{datetime.now()}] Starting monthly Shariah performance fixture update...")

            # Generate the new Shariah AI Performance fixture
            result = SymbolListSerializer.generateShariahAIPerformanceFixture(
                app,
                days_back=90,           # Analyze last 90 days
                min_predictions=10,     # Minimum 10 predictions required
                min_profit_percentage=0.1,  # Minimum 0.1% average profit
                max_stocks=100,         # Top 100 Shariah compliant stocks
                shariah_list_id=1       # Use main Shariah Compliant list
            )

            if result.get('success'):
                shariah_list = result.get('shariah_ai_performance_list', {})
                generation_details = result.get('generation_details', {})
                portfolio_updates = result.get('portfolio_updates', {})

                print(f"[{datetime.now()}] ✅ Shariah AI Performance fixture {result['action']} successfully!")
                print(f"   List ID: {shariah_list.get('id')}")
                print(f"   List Name: {shariah_list.get('name')}")
                print(f"   Symbol Count: {shariah_list.get('symbol_count')}")
                print(f"   Analysis Period: {generation_details.get('analysis_period')}")
                print(f"   Shariah List Used: {generation_details.get('shariah_list_used', {}).get('name')}")
                print(f"   Compliance Rate: {generation_details.get('shariah_compliance_rate')}%")

                # Portfolio update results
                if portfolio_updates.get('success'):
                    update_result = portfolio_updates.get('portfolio_update_result', {})
                    print(f"   Portfolio Updates:")
                    print(f"     Accounts Updated: {update_result.get('total_updated', 0)}")
                    print(f"     Accounts Failed: {update_result.get('total_failed', 0)}")
                    print(f"     Success Rate: {update_result.get('success_rate', 0)}%")
                else:
                    print(f"   Portfolio Updates Failed: {portfolio_updates.get('error')}")

                print(f"[{datetime.now()}] Shariah performance fixture update completed successfully!")

            else:
                print(f"[{datetime.now()}] ❌ Failed to update Shariah performance fixture: {result.get('error')}")
    except Exception as e:
        print(f"❌ UpdateShariahPerformanceFixture cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########             Event Logs            ###################
#################################################################
@apiDecorator.protectedRoute('/events/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getEvents(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = EventSerializer.list(accountUUID, app)
            return response
    except Exception as e:
        print(f"❌ GetEvents endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

#################################################################
###########              Trade Orders         ###################
#################################################################
@apiDecorator.protectedRoute('/trade/order', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def orderTrade():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db

            if app.current_request.json_body:
                data = app.current_request.json_body
            else:
                data = app.current_request._body

            print(f"🔄 Processing trade order: {data.get('action', 'unknown')} for {data.get('symbol', 'unknown')}")

            if data['action'] == 'buy':
                response = AutomationSerializer.buy(data, app)
            elif data['action'] == 'sell':
                print(f"sell function {data}")
                response = AutomationSerializer.sell(data, app)
            elif data['action'] == 'stop_loss':
                response = AutomationSerializer.stop_loss(data, app)
            elif data['action'] == 'cancel':
                print(colored(data, "blue"))
                response = AutomationSerializer.cancel_order(data.get('trade_uuid', None), app)
            else:
                raise APIException(f"Unknown action: {data.get('action', 'none')}")

            print(f"✅ Trade order completed successfully: {response.get('success', False)}")
            return response

    except APIException as e:
        print(f"❌ API Exception in trade order: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'message': str(e)
        }
    except Exception as e:
        print(f"❌ OrderTrade endpoint error: {e}")
        emergency_cleanup()
        return {
            'success': False,
            'error': 'An unexpected error occurred while processing the trade order',
            'message': 'An unexpected error occurred while processing the trade order'
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/trade/cancel/{tradeUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def orderCancel(tradeUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = AutomationSerializer.cancel_order(tradeUUID, app)
            return response
    except Exception as e:
        print(f"❌ OrderCancel endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/trade/cancel_all_orders/{trading_bot_uuid}', methods=['GET'], cors=corsConfig)
def cancelAllOrders(trading_bot_uuid):
    """
    Cancel all open orders for a specific trading bot/automation.
    This endpoint affects one account at a time.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.serializer import AutomationSerializer
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Utils.functions import ChaliceRequest
    from sqlalchemy.orm.exc import NoResultFound
    import traceback

    try:
        with quick_session() as db:
            app.current_request.db = db

            print(f"🚫 Starting cancel all orders for bot: {trading_bot_uuid}")

            # Find the trading bot
            try:
                bot = db.query(TradingBot).filter(
                    TradingBot.uuid == trading_bot_uuid,
                    TradingBot.status_id == 1  # Active bots only
                ).one()
            except NoResultFound:
                return {
                    'success': False,
                    'message': f'Trading bot {trading_bot_uuid} not found or inactive'
                }

            print(f"✅ Found bot: {bot.uuid} for account {bot.account_id}")

            # Get all pending orders for this bot
            pending_orders = db.query(Trades).filter(
                Trades.trading_bot_id == bot.id,
                Trades.status_id == 4  # Status 4 = pending
            ).all()

            print(f"📋 Found {len(pending_orders)} pending orders to cancel")

            cancelled_orders = []
            failed_cancellations = []

            for order in pending_orders:
                try:
                    print(f"🚫 Cancelling order: {order.uuid} - {order.symbol} {order.action_type.name if order.action_type else 'Unknown'}")

                    # Cancel the order using the existing cancel_order method
                    result = AutomationSerializer.cancel_order(order.uuid, app)

                    if result.get('success', False):
                        # Extract the updated trade info from the result
                        trade_data = result.get('trade', {})
                        cancelled_orders.append({
                            'trade_uuid': order.uuid,
                            'symbol': order.symbol,
                            'action': order.action_type.name if order.action_type else 'Unknown',
                            'quantity': float(order.quantity) if order.quantity else 0,
                            'status': 'cancelled',
                            'broker_order_number': trade_data.get('broker_order_number') if trade_data else None,
                            'cancelled_at': trade_data.get('datestamp') if trade_data else None
                        })
                        print(f"✅ Successfully cancelled order {order.uuid}")
                    else:
                        failed_cancellations.append({
                            'trade_uuid': order.uuid,
                            'symbol': order.symbol,
                            'action': order.action_type.name if order.action_type else 'Unknown',
                            'quantity': float(order.quantity) if order.quantity else 0,
                            'error': result.get('message', 'Unknown error')
                        })
                        print(f"❌ Failed to cancel order {order.uuid}: {result.get('message', 'Unknown error')}")

                except Exception as e:
                    error_msg = str(e)
                    failed_cancellations.append({
                        'trade_uuid': order.uuid,
                        'symbol': order.symbol,
                        'action': order.action_type.name if order.action_type else 'Unknown',
                        'quantity': float(order.quantity) if order.quantity else 0,
                        'error': error_msg
                    })
                    print(f"❌ Exception cancelling order {order.uuid}: {error_msg}")

            # Summary
            total_orders = len(pending_orders)
            successful_cancellations = len(cancelled_orders)
            failed_count = len(failed_cancellations)

            print(f"📊 Cancellation Summary: {successful_cancellations}/{total_orders} successful")

            return {
                'success': True,
                'bot_uuid': trading_bot_uuid,
                'account_id': bot.account_id,
                'summary': {
                    'total_pending_orders': total_orders,
                    'successful_cancellations': successful_cancellations,
                    'failed_cancellations': failed_count
                },
                'cancelled_orders': cancelled_orders,
                'failed_cancellations': failed_cancellations,
                'message': f'Processed {total_orders} orders: {successful_cancellations} cancelled, {failed_count} failed'
            }

    except Exception as e:
        print(f"❌ CancelAllOrders endpoint error: {e}")
        print(traceback.format_exc())
        emergency_cleanup()
        return {
            'success': False,
            'message': f'Error cancelling orders: {str(e)}'
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None



#################################################################
###########              AI Scores      ###################
#################################################################
@apiDecorator.protectedRoute('/ai/score/{symbol}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getAIscore(symbol):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.getAIscore(symbol,app)
            return response
    except Exception as e:
        print(f"❌ GetAIscore endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/symbols/prune/{accountUUID}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def pruneScores(accountUUID):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = PortfolioSerializer.prune_losers(accountUUID,app)
            return response
    except Exception as e:
        print(f"❌ PruneScores endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

#################################################################
###########              Stripe Checkout      ###################
#################################################################
@apiDecorator.protectedRoute('/stripe/subscription/update', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def updateSubscription():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = SubscriptionSerializer._update(data,app)
            return response
    except Exception as e:
        print(f"❌ UpdateSubscription endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/stripe/subscription/overdue', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def overduePayment():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = SubscriptionSerializer._update(data,app)
            return response
    except Exception as e:
        print(f"❌ OverduePayment endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/stripe/subscription/cancel', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def cancelSubscription():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = SubscriptionSerializer._cancel(data,app)
            return response
    except Exception as e:
        print(f"❌ CancelSubscription endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

#################################################################
###########              Contact Form         ###################
#################################################################
@apiDecorator.protectedRoute('/email/contact', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def contact():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = Contact.contactEmail(data, app)
            return response
    except Exception as e:
        print(f"❌ Contact endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########             Cognito Lambda        ###################
#################################################################
@app.lambda_function(name='verification_code_message')
def verificationCodeMessage(event, context):
    print(event)
    if event['triggerSource'] == "CustomMessage_SignUp":
        response = cognito.SendCode(event)
    elif event['triggerSource'] == "CustomMessage_AdminCreateUser":
        response = cognito.SendCode(event)
    elif event['triggerSource'] == "CustomMessage_ResendCode":
        response = cognito.SendCode(event)
    elif event['triggerSource'] == "CustomMessage_ForgotPassword":
        response = cognito.SendCode(event)
    elif event['triggerSource'] == "CustomMessage_UpdateUserAttribute":
        pass
    elif event['triggerSource'] == "CustomMessage_VerifyUserAttribute":
        pass
    elif event['triggerSource'] == "CustomMessage_Authentication":
        pass
    else:
        pass
    print('response', response)
    return response


#################################################################
###########           Automation Lambdas      ###################
#################################################################
@app.schedule(Cron('0', '*', '?', '*', '*', '*'))
def UpdateAutomations(event):
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest

    try:
        with quick_session() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": None
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            automations = db.query(TradingBot).filter(TradingBot.status_id.in_([1,2,15])).all()
            for a in automations:
                ## Renew access tokens
                if a.bot_type_id in [1,2]:
                    BotSerializer.renewBotToken(a.uuid, app)
                    pass
    except Exception as e:
        print(f"❌ UpdateAutomations cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.schedule(Cron('*/30', '10-23', '?', '*', '*', '*'))
def updateAutomationBalances(event):
    """
    Update automation balances cron job that runs every 30 minutes.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Utils.database import get_db_session_with_retry

    print("💰 Starting automation balance updates...")

    try:
        with get_db_session_with_retry() as db:
            automations = db.query(TradingBot).filter(TradingBot.status_id.in_([1,2])).all()

            for a in automations:
                ## Update account balances
                if a.brokerage_connected:
                    body = {
                        "automation_uuid": a.uuid,
                        "account_uuid": a.account.uuid
                    }
                    payload = {
                        "multiValueQueryStringParameters": None,
                        "headers": {
                            "x-api-key": os.environ['AI_API_KEY'],
                            "access-token": None,
                            "refresh-token": None,
                            "account-id": None
                        },
                        "pathParameters": None,
                        "requestContext": {
                            "httpMethod": None,
                            "resourcePath": None
                        },
                        "body": None,
                        "stageVariables": {
                            "stage": os.environ['STAGE']
                        }
                    }
                    payload['body'] = json.dumps(body)

                    ## Call the lambda
                    lambdaName = os.environ['BALANCE_UPDATE_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )

            print(f"✅ Balance updates triggered for {len(automations)} automations")

    except Exception as e:
        print(f"❌ Error in updateAutomationBalances: {e}")
        import traceback
        traceback.print_exc()


@app.lambda_function(name='balance_update')
def UpdateBalance(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])

            BotSerializer.update_balance(body['automation_uuid'], app)
            AccountSerializer.calculateBalance(body['account_uuid'], app)
    except Exception as e:
        print(f"❌ UpdateBalance lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


########################################
##  Update order statuses and import brokerage orders (Optimized)
########################################
@app.schedule(Cron('*/15', '04-23', '?', '*', '2-6', '*'))
def updateOrderStatuses(event):
    """
    Update order statuses and import brokerage orders.
    Optimized to run every 15 minutes during all trading hours (04:00-24:00 UTC).
    Includes smart filtering to reduce lambda costs.
    """
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import unixTimeNow

    print("📊 Starting optimized order status update...")

    try:
        with get_db_session_with_retry() as db:
            # Only process bots that have pending orders or recent activity
            # This reduces unnecessary lambda invocations
            recent_threshold = unixTimeNow() - (24 * 60 * 60)  # Last 24 hours

            # Get bots with pending orders or recent trades
            bots_with_activity = db.query(TradingBot).join(Trades).filter(
                TradingBot.status_id == 1,  # Active bots only
                TradingBot.brokerage_connected == True,  # Connected bots only
                or_(
                    Trades.status_id == 4,  # Has pending orders
                    Trades.created_at >= recent_threshold  # Has recent activity
                )
            ).distinct().all()

            if not bots_with_activity:
                print("✅ No bots with pending orders or recent activity")
                return

            print(f"🤖 Processing {len(bots_with_activity)} bots with activity...")

            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            # Process pending trades for bots with activity
            pending_trades = db.query(Trades).filter(
                Trades.status_id == 4,  # Pending status
                Trades.trading_bot_id.in_([bot.id for bot in bots_with_activity])
            ).all()

            print(f"📋 Processing {len(pending_trades)} pending trades...")

            for t in pending_trades:
                ## Update account balances
                if t.automation.brokerage_connected:
                    body = {
                        "trade_uuid": t.uuid
                    }

                    payload['body'] = json.dumps(body)

                    ## Call the lambda
                    lambdaName = os.environ['UPDATE_ORDER_STATUS_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )

            # Import orders only for bots with activity
            print(f"📥 Importing orders for {len(bots_with_activity)} active bots...")
            for a in bots_with_activity:
                    body = {
                        "automation_uuid": a.uuid
                    }

                    payload['body'] = json.dumps(body)

                    ## Call the lambda
                    lambdaName = os.environ['IMPORT_BROKERAGE_ORDERS_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )

            print(f"✅ Order status update completed. Processed {len(pending_trades)} trades and {len(bots_with_activity)} bots")

    except Exception as e:
        print(f"❌ Error in orderStatusUpdate: {e}")
        import traceback
        traceback.print_exc()


@app.lambda_function(name='update_order_status_lambda')
def UpdateOrderStatus(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])

            BotSerializer._update_order_status(body['trade_uuid'], app)
    except Exception as e:
        print(f"❌ UpdateOrderStatus lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.lambda_function(name='import_brokerage_orders')
def ImportBrokerageOrders(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import json

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            result = AutomationSerializer.import_stock_orders(body['automation_uuid'], app)
            return result
    except Exception as e:
        print(f"❌ ImportBrokerageOrders lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


########################################
##  Cleans up Automations that were incorrectly setup
########################################
@app.schedule(Cron('0', '8', '*', '*', '?', '*'))
def cleanupAutomations(event):
    """
    Cleanup automations cron job that runs daily at 8:00 AM UTC.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import ChaliceRequest

    print("🧹 Starting automation cleanup...")

    try:
        with get_db_session_with_retry() as db:
            automations = db.query(TradingBot).filter(TradingBot.status_id == 4).all()
            cleanup_count = 0

            for a in automations:
                try:
                    a.status_id = 8
                    cleanup_count += 1
                except Exception as e:
                    print(f"❌ Error removing automation {a.id}: {e}")
                    import traceback
                    traceback.print_exc()
                    db.rollback()
                    continue

            if cleanup_count > 0:
                db.commit()
                print(f"✅ Cleaned up {cleanup_count} automations")
            else:
                print("✅ No automations to cleanup")

    except Exception as e:
        print(f"❌ Error in cleanupAutomations: {e}")
        import traceback
        traceback.print_exc()


########################################
##  Update the account balance sheets
########################################
#################################################################
###########        Transfer Updates Cron Job   ###################
#################################################################
@app.schedule(Cron('0', '*/1', '*', '*', '?', '*'))  # Run every 3 hours
def updateTransfers(event):
    """
    Automated cron job to update transfers (deposits, withdrawals, dividends, etc.)
    from all connected brokerages every 3 hours.

    This cron job:
    1. Fetches all active trading bots with brokerage connections
    2. Retrieves transfer data from each brokerage API
    3. Creates/updates transfer records in the database
    4. Handles deposits, withdrawals, dividends, interest, and fees
    5. Maintains brokerage-agnostic transfer tracking
    """
    import os
    from chalicelib.Bots.transferSerializer import TransferSerializer
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from datetime import datetime
    import traceback

    print("🔄 Starting automated transfer updates...")

    try:
        # Set up the request context
        context = {
            "multiValueQueryStringParameters": None,
            "headers": {
                "x-api-key": os.environ['AI_API_KEY'],
                "access-token": None,
                "refresh-token": None,
                "account-id": None
            },
            "pathParameters": None,
            "requestContext": {
                "httpMethod": None,
                "resourcePath": None
            },
            "body": None,
            "stageVariables": {
                "stage": os.environ['STAGE']
            },
            "query_params": {}
        }

        with quick_session() as db:
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            # Update all transfers
            results = TransferSerializer.update_all_transfers(app)

        print(f"✅ Transfer update completed:")
        print(f"   📊 Total bots processed: {results['total_bots']}")
        print(f"   ✅ Successful updates: {results['successful_updates']}")
        print(f"   ❌ Failed updates: {results['failed_updates']}")

        if results['errors']:
            print("🚨 Errors encountered:")
            for error in results['errors']:
                print(f"   - {error}")

            return {
                'statusCode': 200,
                'body': f"Transfer updates completed: {results['successful_updates']}/{results['total_bots']} successful"
            }
    except Exception as e:
        print(f"❌ UpdateTransfers cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.schedule(Cron('0', '8', '*', '*', '?', '*'))
def balanceSheets():
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest

    try:
        with quick_session() as db:
            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            automations = db.query(TradingBot).filter(TradingBot.status_id != 8).all()
            for a in automations:
                body = {
                    "automation_uuid": a.uuid
                }

                payload['body'] = json.dumps(body)

                ## Call the lambda
                lambdaName = os.environ['UPDATE_BALANCE_SHEETS_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )
    except Exception as e:
        print(f"❌ BalanceSheets cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.lambda_function(name='update_balance_sheets_lambda')
def UpdateBalanceSheets(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import json

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            result = AutomationSerializer.update_balance_sheet(body['automation_uuid'], app)
            return result
    except Exception as e:
        print(f"❌ UpdateBalanceSheets lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########           Sagemaker Scaling       ###################
#################################################################

# ###### Smart portfolio process
# @app.schedule(Cron('15', '19', '?', '*', '2-6', '*'))
# def createSageMakerEndpointClose(event):
#     SageMakerSerializer.create_endpoint()


# @app.schedule(Cron('50', '19', '?', '*', '2-6', '*'))
# def deleteSageMakerEndpointClose(event):
#     SageMakerSerializer.delete_endpoint()


# @app.schedule(Cron('28', '19', '?', '*', '2-6', '*'))
# def checkSageMakerEndpointClose(event):
#     SageMakerSerializer.describe_endpoint()


# @app.schedule(Cron('20', '13', '?', '*', '2-6', '*'))
# def createSageMakerEndpointOpen(event):
#     SageMakerSerializer.create_endpoint()


# @app.schedule(Cron('28', '13', '?', '*', '2-6', '*'))
# def checkSageMakerEndpointOpen(event):
#     SageMakerSerializer.describe_endpoint()


# @app.schedule(Cron('40', '13', '?', '*', '2-6', '*'))
# def deleteSageMakerEndpointOpen(event):
#     SageMakerSerializer.delete_endpoint()


# ## Stop Loss signals
# @app.schedule(Cron('20', '13-18', '?', '*', '2-6', '*'))
# def createSageMakerEndpointSL(event):
#     SageMakerSerializer.create_endpoint()


# @app.schedule(Cron('40', '13-18', '?', '*', '2-6', '*'))
# def deleteSageMakerEndpointSL(event):
#     SageMakerSerializer.delete_endpoint()


# @app.schedule(Cron('28', '13-18', '?', '*', '2-6', '*'))
# def checkSageMakerEndpointSL(event):
#     SageMakerSerializer.describe_endpoint()

# @app.schedule(Cron('20', '19', '?', '*', '2-6', '*'))
# def createSageMakerEndpointDT(event):
#     SageMakerSerializer.create_endpoint()


# @app.schedule(Cron('40', '19', '?', '*', '2-6', '*'))
# def deleteSageMakerEndpointDT(event):
#     SageMakerSerializer.delete_endpoint()


# @app.schedule(Cron('28', '19', '?', '*', '2-6', '*'))
# def checkSageMakerEndpointDT(event):
#     SageMakerSerializer.describe_endpoint()

@app.schedule(Cron('52', '7', '?', '*', '2-6', '*'))
def createSageMakerEndpointDT(event):
    SageMakerSerializer.create_endpoint()


@app.schedule(Cron('59', '23', '?', '*', '2-6', '*'))
def deleteSageMakerEndpointDT(event):
    SageMakerSerializer.delete_endpoint()


@app.schedule(Cron('0', '8', '?', '*', '2-6', '*'))
def checkSageMakerEndpointDT(event):
    SageMakerSerializer.describe_endpoint()

#################################################################
###########             AI Cron Lambdas       ###################
#################################################################
@app.schedule(Cron('0', '8', '?', '*', '2-6', '*'))
def analyzeSymbolsPremarket(event):
    """
    AI analysis cron job that runs at 04:00 UTC (start of premarket).
    Triggers symbol analysis for all active portfolios to prepare for automated trading.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Portfolios.serializer import PortfolioSerializer
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import ChaliceRequest

    print("🌅 Starting AI analysis for premarket session...")

    try:
        with get_db_session_with_retry() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": "last_month"
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            ## Get symbols
            symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app)

            ## Send symbols to queue
            for s in symbols:
                body = {
                    "symbol": s,
                    "market_session": "premarket"
                }
                payload = {
                    "multiValueQueryStringParameters": None,
                    "headers": {
                        "x-api-key": os.environ['AI_API_KEY'],
                        "access-token": None,
                        "refresh-token": None,
                        "account-id": None
                    },
                    "pathParameters": None,
                    "requestContext": {
                        "httpMethod": None,
                        "resourcePath": None
                    },
                    "body": None,
                    "stageVariables": {
                        "stage": os.environ['STAGE']
                    }
                }
                payload['body'] = json.dumps(body)

                ## Call the lambda
                lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )

            print(f"✅ Premarket AI analysis triggered for {len(symbols)} symbols")

    except Exception as e:
        print(f"❌ Error in analyzeSymbolsPremarket: {e}")
        import traceback
        traceback.print_exc()


@app.schedule(Cron('10', '8', '?', '*', '2-6', '*'))
def processPremarketPortfolios(event):
    """
    Premarket portfolio processing cron job that runs at 04:10 UTC (10 minutes after symbol analysis).
    Processes portfolios for accounts with automation balance over $25,000 and available position slots.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Accounts.models import Account, AccountBalance
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Portfolios.models import Portfolio, AccountPortfolio

    print("🌅 Starting premarket portfolio processing...")

    try:
        with quick_session() as db:
            # Get all active automations with connected brokerages
            automations = db.query(TradingBot).filter(
                TradingBot.status_id == 1,
                TradingBot.brokerage_connected == True
            ).all()

            processed_count = 0
            eligible_count = 0

            for automation in automations:
                try:
                    # Get account balance
                    balance = db.query(AccountBalance).filter(
                        AccountBalance.account_id == automation.account_id
                    ).order_by(AccountBalance.created_at.desc()).first()

                    if not balance:
                        print(f"⚠️ No balance found for account {automation.account_id}")
                        continue

                    account_balance = float(balance.balance)

                    # Check if account balance is over $25,000 (PDT exempt)
                    if account_balance < 25000:
                        print(f"💰 Account {automation.account_id} balance ${account_balance:,.2f} < $25,000 - skipping")
                        continue

                    eligible_count += 1

                    # Get account portfolio settings
                    account_portfolio = db.query(AccountPortfolio).filter(
                        AccountPortfolio.account_id == automation.account_id
                    ).first()

                    if not account_portfolio:
                        print(f"⚠️ No portfolio settings for account {automation.account_id}")
                        continue

                    # Check available cash ratio
                    if account_portfolio.available_cash_ratio >= 100:
                        print(f"💸 Account {automation.account_id} has 100% cash reserved - skipping")
                        continue

                    # Check current active positions
                    active_positions = db.query(Portfolio).filter(
                        Portfolio.account_id == automation.account_id,
                        Portfolio.active == True,
                        Portfolio.quantity > 0
                    ).count()

                    # Assume max 4 positions (can be made configurable later)
                    max_positions = 4
                    available_slots = max_positions - active_positions

                    if available_slots <= 0:
                        print(f"📊 Account {automation.account_id} has {active_positions}/{max_positions} positions - no slots available")
                        continue

                    print(f"✅ Processing account {automation.account_id}: ${account_balance:,.2f} balance, {available_slots} slots available")

                    # Create payload for portfolio processing
                    body = {
                        "account_uuid": automation.account.uuid,
                        "daytrade": False  # Regular portfolio processing, not day trading
                    }

                    payload = {
                        "multiValueQueryStringParameters": None,
                        "headers": {
                            "x-api-key": os.environ['AI_API_KEY'],
                            "access-token": None,
                            "refresh-token": None,
                            "account-id": None
                        },
                        "pathParameters": None,
                        "requestContext": {
                            "httpMethod": None,
                            "resourcePath": None
                        },
                        "body": json.dumps(body),
                        "stageVariables": {
                            "stage": os.environ['STAGE']
                        }
                    }

                    # Call the portfolio processing lambda
                    lambdaName = os.environ['SMART_PORTFOLIO_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )

                    processed_count += 1
                    print(f"📤 Portfolio processing triggered for account {automation.account_id}")

                except Exception as e:
                    print(f"❌ Error processing account {automation.account_id}: {e}")
                    continue

            print(f"🎯 Premarket portfolio processing complete: {processed_count}/{eligible_count} eligible accounts processed")

    except Exception as e:
        print(f"❌ Error in processPremarketPortfolios: {e}")
        emergency_cleanup()
        import traceback
        traceback.print_exc()
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.schedule(Cron('*/30', '8-13', '?', '*', '2-6', '*'))
def analyzeSymbolsMarketOpen(event):
    """
    AI analysis cron job that runs every 30 minutes during market hours.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Portfolios.serializer import PortfolioSerializer
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import ChaliceRequest

    print("🤖 Starting AI analysis for market open session...")

    try:
        with get_db_session_with_retry() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": "last_month"
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            ## Get symbols
            symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app)

            ## Send symbols to queue
            for s in symbols:
                body = {
                    "symbol": s,
                    "market_session": "market_open"
                }
                payload = {
                    "multiValueQueryStringParameters": None,
                    "headers": {
                        "x-api-key": os.environ['AI_API_KEY'],
                        "access-token": None,
                        "refresh-token": None,
                        "account-id": None
                    },
                    "pathParameters": None,
                    "requestContext": {
                        "httpMethod": None,
                        "resourcePath": None
                    },
                    "body": None,
                    "stageVariables": {
                        "stage": os.environ['STAGE']
                    }
                }
                payload['body'] = json.dumps(body)

                ## Call the lambda
                lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )

            print(f"✅ AI analysis triggered for {len(symbols)} symbols during market open")

    except Exception as e:
        print(f"❌ Error in analyzeSymbolsMarketOpen: {e}")
        import traceback
        traceback.print_exc()



@app.schedule(Cron('30', '19', '?', '*', '2-6', '*'))
def analyzeSymbolsMarketClose(event):
    """
    AI analysis cron job that runs at market close.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Portfolios.serializer import PortfolioSerializer
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import ChaliceRequest

    print("🤖 Starting AI analysis for market close session...")

    try:
        with get_db_session_with_retry() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": "last_month"
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            ## Get symbols
            symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app)

            ## Send symbols to queue
            for s in symbols:
                body = {
                    "symbol": s,
                    "market_session": "market_close"
                }
                payload = {
                    "multiValueQueryStringParameters": None,
                    "headers": {
                        "x-api-key": os.environ['AI_API_KEY'],
                        "access-token": None,
                        "refresh-token": None,
                        "account-id": None
                    },
                    "pathParameters": None,
                    "requestContext": {
                        "httpMethod": None,
                        "resourcePath": None
                    },
                    "body": None,
                    "stageVariables": {
                        "stage": os.environ['STAGE']
                    }
                }
                payload['body'] = json.dumps(body)

                ## Call the lambda
                lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )

            print(f"✅ AI analysis triggered for {len(symbols)} symbols at market close")

    except Exception as e:
        print(f"❌ Error in analyzeSymbolsMarketClose: {e}")
        import traceback
        traceback.print_exc()


@app.schedule(Cron('30', '14', '?', '*', '2-6', '*'))
def analyzeSymbolsStopLossOrders(event):
    """
    Stop loss analysis cron job that runs at 18:30 UTC.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Portfolios.serializer import PortfolioSerializer
    from chalicelib.Portfolios.models import Portfolio
    from chalicelib.Accounts.models import Account
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import ChaliceRequest

    print("🛑 Starting stop loss analysis session...")

    try:
        with get_db_session_with_retry() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": "last_month"
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            ## Get symbols with open trades
            symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app, STOP_LOSS=True)
            symbols = list(set(symbols))

            ## Send symbols to lambdas
            for s in symbols:
                body = {
                    "symbol": s,
                    "market_session": "stop_loss"
                }
                payload = {
                    "multiValueQueryStringParameters": None,
                    "headers": {
                        "x-api-key": os.environ['AI_API_KEY'],
                        "access-token": None,
                        "refresh-token": None,
                        "account-id": None
                    },
                    "pathParameters": None,
                    "requestContext": {
                        "httpMethod": None,
                        "resourcePath": None
                    },
                    "body": None,
                    "stageVariables": {
                        "stage": os.environ['STAGE']
                    }
                }
                payload['body'] = json.dumps(body)

                ## Call the lambda
                lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )

            print(f"✅ Stop loss analysis triggered for {len(symbols)} symbols")

    except Exception as e:
        print(f"❌ Error in analyzeSymbolsStopLossOrders: {e}")
        import traceback
        traceback.print_exc()


# @app.schedule(Cron('30', '13-14', '?', '*', '2-6', '*'))
# def analyzeSymbolsStopLossOrdersHighRate(event):
#     from chalicelib.Portfolios.serializer import PortfolioSerializer
#     from chalicelib.Portfolios.models import Portfolio
#     from chalicelib.Accounts.models import Account
#     from chalicelib.Bots.models import TradingBot, Trades
#     from chalicelib.Utils.database import conn
#     from chalicelib.Utils.functions import ChaliceRequest
#     db = conn()
#     context = {
#         "multiValueQueryStringParameters": None,
#         "headers": {
#             "x-api-key": os.environ['AI_API_KEY'],
#             "access-token": None,
#             "refresh-token": None,
#             "account-id": None
#         },
#         "pathParameters": None,
#         "requestContext": {
#             "httpMethod": None,
#             "resourcePath": None
#         },
#         "body": None,
#         "stageVariables": {
#             "stage": os.environ['STAGE']
#         },
#         "query_params": {
#             "timespan": "last_month"
#         }
#     }
#     app.current_request = ChaliceRequest(context)
#     app.current_request.db = conn()

#     ## Get symbols with open trades
#     symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app, STOP_LOSS=True)

#     symbols = list(set(symbols))

#     ## Send symbols to lambdas
#     for s in symbols:
#         body = {
#             "symbol": s,
#             "market_session": "stop_loss"
#         }
#         payload = {
#             "multiValueQueryStringParameters": None,
#             "headers": {
#                 "x-api-key": os.environ['AI_API_KEY'],
#                 "access-token": None,
#                 "refresh-token": None,
#                 "account-id": None
#             },
#             "pathParameters": None,
#             "requestContext": {
#                 "httpMethod": None,
#                 "resourcePath": None
#             },
#             "body": None,
#             "stageVariables": {
#                 "stage": os.environ['STAGE']
#             }
#         }
#         payload['body'] = json.dumps(body)

#         ## Call the lambda
#         lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
#         lambda_response = boto3.client('lambda').invoke(
#             FunctionName=lambdaName,
#             InvocationType='Event',
#             Payload=json.dumps(payload)
#         )
#     db.close()
# Connection is managed by the container session - no manual close needed

##############################################
## Daytrading function
##############################################
@app.schedule(Cron('30', '8-18', '?', '*', '2-6', '*'))
def analyzeSymbolsDaytrade(event):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Portfolios.models import Portfolio

    try:
        with quick_session() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": "last_month"
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            ## Get automations
            automations = db.query(TradingBot).filter(TradingBot.day_trader == True, TradingBot.status_id == 1).all()

            for a in automations:
                portfolio = db.query(Portfolio).filter(Portfolio.account_id == a.account_id, Portfolio.status_id == 1, Portfolio.hold == False).all()
                for p in portfolio:
                    body = {
                        "symbol": p.symbol,
                        "market_session": "daytrade",
                        "bot_uuid": a.uuid
                    }
                    payload = {
                        "multiValueQueryStringParameters": None,
                        "headers": {
                            "x-api-key": os.environ['AI_API_KEY'],
                            "access-token": None,
                            "refresh-token": None,
                            "account-id": None
                        },
                        "pathParameters": None,
                        "requestContext": {
                            "httpMethod": None,
                            "resourcePath": None
                        },
                        "body": None,
                        "stageVariables": {
                            "stage": os.environ['STAGE']
                        }
                    }
                    payload['body'] = json.dumps(body)

                    ## Call the lambda
                    lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )
    except Exception as e:
        print(f"❌ AnalyzeSymbolsDaytrade cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.schedule(Cron('35', '8-18', '?', '*', '2-6', '*'))
def DaytradeSmartPortfolio(event):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Accounts.models import Account
    from chalicelib.Bots.models import TradingBot

    try:
        with quick_session() as db:
            ## Get automations
            automations = db.query(TradingBot).filter(TradingBot.day_trader == True, TradingBot.status_id == 1).all()

            ## Create the payload
            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            for a in automations:
                body = {
                    "account_uuid": a.account.uuid,
                    "daytrade": True
                }
                payload['body'] = json.dumps(body)
                ## Call the lambda
                lambdaName = os.environ['SMART_PORTFOLIO_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )
                # print(lambda_response)
    except Exception as e:
        print(f"❌ DaytradeSmartPortfolio cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


##### Update the symbol active status
@app.schedule(Cron('*/15', '04-23', '?', '*', '2-6', '*'))
def positionUpdate(event):
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            automations = db.query(TradingBot).filter(TradingBot.status_id == 1, TradingBot.brokerage_connected == True).all()
            for a in automations:
                ## Update account balances
                if a.brokerage_connected:
                    body = {
                        "automation_uuid": a.uuid
                    }
                    payload = {
                        "multiValueQueryStringParameters": None,
                        "headers": {
                            "x-api-key": os.environ['AI_API_KEY'],
                            "access-token": None,
                            "refresh-token": None,
                            "account-id": None
                        },
                        "pathParameters": None,
                        "requestContext": {
                            "httpMethod": None,
                            "resourcePath": None
                        },
                        "body": None,
                        "stageVariables": {
                            "stage": os.environ['STAGE']
                        }
                    }
                    payload['body'] = json.dumps(body)

                    ## Call the lambda
                    lambdaName = get_lambda_arn(os.environ['POSITION_UPDATE_LAMBDA'])
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )
    except Exception as e:
        print(f"❌ PositionUpdate cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.lambda_function(name='position_updates')
def PositionUpdateLambda(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import json

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            result = AutomationSerializer.update_positions(body['automation_uuid'], app)
            return result
    except Exception as e:
        print(f"❌ PositionUpdateLambda lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


##### Update the symbol cooldown status
@app.schedule(Cron('0', '0', '?', '*', '2-6', '*'))
def cooldownUpdate(event):
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Portfolios.models import Portfolio
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            symbols = db.query(Portfolio).filter(Portfolio.cooldown == True).all()
            for s in symbols:
                s.cooldown = False
            try:
                db.commit()
            except Exception as e:
                db.rollback()
                print(e)
                print(traceback.format_exc())
    except Exception as e:
        print(f"❌ CooldownUpdate cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


##### initialize SMART portfolio
@app.schedule(Cron('35', '19', '?', '*', '2-6', '*'))
def initSmartPortfolio(event):
    from chalicelib.Accounts.models import Account
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Portfolios.models import Portfolio
    from chalicelib.Bots.models import TradingBot
    from chalicelib.lambda_resolver import get_lambda_arn

    try:
        with quick_session() as db:
            # Set up the app context for scheduled Lambda
            app.current_request = ChaliceRequest({})
            app.current_request.db = db

            ## Get symbols
            accounts = db.query(Account).filter(Account.status_id == 1).all()

            ## Create the payload
            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            for a in accounts:
                body = {
                    "account_uuid": a.uuid
                }
                payload['body'] = json.dumps(body)
                ## Call the lambda
                lambdaName = get_lambda_arn(os.environ['SMART_PORTFOLIO_LAMBDA'])
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )
                print(lambda_response)
    except Exception as e:
        print(f"❌ InitSmartPortfolio cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.lambda_function(name='process_smart_portfolio')
def ProcessSmartPortfolio(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import json

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            print(f"Processing Portfolio: {body['account_uuid']}")
            result = AutomationSerializer.portfolioProcess(body['account_uuid'], app, DAYTRADE=body.get('daytrade', False))
            return result
    except Exception as e:
        print(f"❌ ProcessSmartPortfolio lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.lambda_function(name='automation_signal')
def ProcessSignal(event, context):
    from sqlalchemy.orm.exc import NoResultFound
    from chalicelib.Bots.models import TradingBot
    from chalicelib.Portfolios.models import Portfolio
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Accounts.models import Account
    from chalicelib.Utils.database import get_db_session_with_retry, force_close_all_connections

    print("🔄 ProcessSignal started - optimized for connection management")

    # Use context manager for proper connection handling
    try:
        with get_db_session_with_retry(max_retries=2) as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            analysis = json.loads(event['body'])

            print(f"📊 Processing signal for symbol: {analysis.get('symbol', 'unknown')}")

            try:
                # Try to find specific bot first
                bot = db.query(TradingBot).filter(
                    TradingBot.uuid == analysis['bot_uuid'],
                    TradingBot.status_id == 1
                ).one()

                print(f"🎯 Found specific bot: {bot.uuid}")
                res = AutomationSerializer.signal(bot, analysis, app)

            except KeyError as k:
                print(f"⚠️ No bot_uuid provided, processing all eligible bots for symbol: {analysis.get('symbol')}")

                # Use more efficient query with joins to reduce database round trips
                bots_with_portfolios = db.query(TradingBot).join(
                    Portfolio, TradingBot.account_id == Portfolio.account_id
                ).filter(
                    TradingBot.status_id == 1,
                    TradingBot.brokerage_connected == True,
                    Portfolio.symbol == analysis['symbol'],
                    Portfolio.status_id == 1
                ).distinct().all()

                print(f"🤖 Found {len(bots_with_portfolios)} eligible bots")

                # Process bots in smaller batches to reduce memory usage
                batch_size = 10
                processed_count = 0

                for i in range(0, len(bots_with_portfolios), batch_size):
                    batch = bots_with_portfolios[i:i + batch_size]

                    for b in batch:
                        try:
                            # We already know this bot has the portfolio from the join
                            analysis['bot_uuid'] = b.uuid
                            res = AutomationSerializer.signal(b, analysis, app)
                            processed_count += 1

                        except Exception as bot_error:
                            print(f"❌ Error processing bot {b.uuid}: {str(bot_error)}")
                            continue

                    # Small delay between batches to prevent overwhelming the system
                    if i + batch_size < len(bots_with_portfolios):
                        import time
                        time.sleep(0.1)

                print(f"✅ Processed {processed_count} bots successfully")

            except Exception as e:
                print(f"❌ Error in ProcessSignal: {str(e)}")
                print(traceback.format_exc())

    except Exception as outer_error:
        print(f"❌ Critical error in ProcessSignal: {str(outer_error)}")
        print(traceback.format_exc())
        # Force close connections on critical error
        force_close_all_connections()

    finally:
        print("🏁 ProcessSignal completed")

#################################################
########  Premarket profit checks
#################################################
@app.schedule(Cron('15', '19', '?', '*', '2-6', '*'))
def prune_check(event):
    import requests
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Portfolios.models import AccountPortfolio, Portfolio

    print("🔍 Starting prune check with optimized connection management...")

    try:
        with get_db_session_with_retry(max_retries=2) as db:
            ## Create the payload
            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            # Optimize query to get accounts and symbols in one go
            acct_symbols = db.query(Portfolio.account_id, Portfolio.symbol).join(
                AccountPortfolio, Portfolio.account_id == AccountPortfolio.account_id
            ).filter(
                AccountPortfolio.prune_losers == 1,
                Portfolio.status_id == 1
            ).distinct().all()

            symbols = list(set([x.symbol for x in acct_symbols]))
            accts = list(set([x.account_id for x in acct_symbols]))

            print(f"📊 Processing {len(symbols)} symbols for {len(accts)} accounts")

            for s in symbols:
                body = {
                    "symbol": s
                }
                if os.environ['STAGE'] != 'local':
                    payload['body'] = json.dumps(body)
                    ## Call the lambda
                    lambdaName = os.environ['PRUNE_CHECK_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='RequestResponse',
                        Payload=json.dumps(payload)
                    )
                    res = json.loads(lambda_response['Payload'].read())
                else:
                    url = f"{os.environ['AI_URL']}/symbol/prune"
                    headers = {
                        "x-api-key": app.current_request.headers['x-api-key']
                    }
                    res = requests.post(url=url, headers=headers, json=body)
                    res = res.json()

                if res.get('status_id'):
                    # Batch update for efficiency
                    db.query(Portfolio).filter(
                        Portfolio.account_id.in_(accts),
                        Portfolio.symbol == s
                    ).update(
                        {"status_id": res['status_id']},
                        synchronize_session=False
                    )

                    try:
                        db.commit()
                    except Exception as e:
                        print(f"❌ Error updating portfolio status for {s}: {e}")
                        db.rollback()

            print("✅ Prune check completed successfully")

    except Exception as e:
        print(f"❌ Error in prune check: {str(e)}")
        print(traceback.format_exc())


#################################################
########  Premarket profit checks (4:00-13:30 UTC)
#################################################
# @app.schedule(Cron('*/10', '04-13', '?', '*', '2-6', '*'))
# def takeProfitPremarket(event):
#     """
#     Take profit monitoring during premarket hours (4:00-13:30 UTC).
#     Runs every 10 minutes to check for profit opportunities.
#     OPTIMIZED for minimal database connections.
#     """
#     from chalicelib.Utils.database import get_db_session
#     from chalicelib.Bots.models import TradingBot, Trades
#     from chalicelib.Portfolios.models import AccountPortfolio

#     print("🌅 Starting premarket take profit check (optimized)...")

#     try:
#         with get_db_session() as db:
#             ## Create the payload
#             payload = {
#                 "multiValueQueryStringParameters": None,
#                 "headers": {
#                     "x-api-key": os.environ['AI_API_KEY'],
#                     "access-token": None,
#                     "refresh-token": None,
#                     "account-id": None
#                 },
#                 "pathParameters": None,
#                 "requestContext": {
#                     "httpMethod": None,
#                     "resourcePath": None
#                 },
#                 "body": None,
#                 "stageVariables": {
#                     "stage": os.environ['STAGE']
#                 }
#             }

#             # Optimized query with join to reduce database calls
#             automations = db.query(TradingBot).join(
#                 AccountPortfolio, TradingBot.account_id == AccountPortfolio.account_id
#             ).filter(
#                 TradingBot.status_id == 1,
#                 AccountPortfolio.take_profit == 1
#             ).all()

#             print(f"📊 Processing {len(automations)} automations for premarket take profit...")

#             # Process in batches to avoid overwhelming Lambda
#             batch_size = 20
#             for i in range(0, len(automations), batch_size):
#                 batch = automations[i:i + batch_size]

#                 for a in batch:
#                     body = {
#                         "bot_uuid": a.uuid
#                     }
#                     payload['body'] = json.dumps(body)
#                     ## Call the lambda
#                     lambdaName = os.environ['TAKE_PROFIT_LAMBDA']
#                     lambda_response = boto3.client('lambda').invoke(
#                         FunctionName=lambdaName,
#                         InvocationType='Event',
#                         Payload=json.dumps(payload)
#                     )

#                 # Small delay between batches
#                 if i + batch_size < len(automations):
#                     import time
#                     time.sleep(0.5)

#             print(f"✅ Premarket take profit check completed for {len(automations)} automations")

#     except Exception as e:
#         print(f"❌ Error in premarket take profit: {str(e)}")
#         print(traceback.format_exc())


#################################################
########  Regular market profit checks (13:30-20:00 UTC)
#################################################
@app.schedule(Cron('*/10', '04-19', '?', '*', '2-6', '*'))
def takeProfitCron(event):
    """
    Regular market take profit monitoring (13:30-20:00 UTC).
    OPTIMIZED for minimal database connections.
    """
    from chalicelib.Utils.database import get_db_session
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Portfolios.models import AccountPortfolio
    import time

    print("📈 Starting regular market take profit check (optimized)...")

    try:
        with get_db_session() as db:
            ## Create the payload
            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            # Optimized query with join
            automations = db.query(TradingBot).join(
                AccountPortfolio, TradingBot.account_id == AccountPortfolio.account_id
            ).filter(
                TradingBot.status_id == 1,
                AccountPortfolio.take_profit == 1
            ).all()

            print(f"📊 Processing {len(automations)} automations for regular market take profit...")

            for a in automations:
                body = {
                    "bot_uuid": a.uuid
                }
                ### RH Throttle
                if a.bot_type_id == 3:
                    time.sleep(3)
                payload['body'] = json.dumps(body)
                ## Call the lambda
                lambdaName = os.environ['TAKE_PROFIT_LAMBDA']
                lambda_response = boto3.client('lambda').invoke(
                    FunctionName=lambdaName,
                    InvocationType='Event',
                    Payload=json.dumps(payload)
                )

            print(f"✅ Regular market take profit check completed for {len(automations)} automations")

    except Exception as e:
        print(f"❌ Error in regular market take profit: {str(e)}")
        print(traceback.format_exc())


@app.schedule(Cron('*/10', '20-23', '?', '*', '2-6', '*'))
def takeProfitExtendedHours(event):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Portfolios.models import AccountPortfolio
    from chalicelib.Utils.functions import ChaliceRequest

    try:
        with quick_session() as db:
            context = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                },
                "query_params": {
                    "timespan": "last_month"
                }
            }
            app.current_request = ChaliceRequest(context)
            app.current_request.db = db

            acctPreferences = db.query(AccountPortfolio).filter(AccountPortfolio.take_profit == 1).all()
            accts = [x.account_id for x in acctPreferences]
            automations = db.query(TradingBot).filter(TradingBot.status_id == 1, TradingBot.account_id.in_(accts)).all()

            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }

            for a in automations:
                ## Get balance
                auto, total_balance, available_cash = BotSerializer._get_balance(a.uuid, app)
                if total_balance > 25000:
                    body = {
                        "bot_uuid": a.uuid
                    }
                    payload['body'] = json.dumps(body)
                    ## Call the lambda
                    lambdaName = os.environ['TAKE_PROFIT_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )
    except Exception as e:
        print(f"❌ TakeProfitExtendedHours cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################
########  Sell Order Monitoring (All Trading Hours)
#################################################
@app.schedule(Cron('*/5', '04-23', '?', '*', '2-6', '*'))
def monitorSellOrders(event):
    """
    Monitor pending sell orders and cancel/replace unfilled orders after 5 minutes.
    Runs every 5 minutes during all trading hours (premarket 04:00-13:30, regular 13:30-20:00, extended 20:00-24:00 UTC).
    Optimized to reduce lambda costs by batching operations.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Utils.functions import unixTimeNow
    import time

    print("🔍 Starting sell order monitoring...")

    try:
        with quick_session() as db:
            # Calculate 5 minutes ago timestamp
            five_minutes_ago = unixTimeNow() - (5 * 60)

            # Find pending sell orders older than 5 minutes
            # Status 4 = pending, action_type_id 8 = limit sell, action_type_id 2 = market sell
            stale_orders = db.query(Trades).filter(
                Trades.status_id == 4,  # Pending status
                Trades.action_type_id.in_([2, 8]),  # Sell orders
                Trades.order_time <= five_minutes_ago  # Older than 5 minutes
            ).all()

            if not stale_orders:
                print("✅ No stale sell orders found")
                return

            print(f"⚠️ Found {len(stale_orders)} stale sell orders to process")

            # Group orders by bot to optimize lambda calls
            orders_by_bot = {}
            for order in stale_orders:
                bot_id = order.trading_bot_id
                if bot_id not in orders_by_bot:
                    orders_by_bot[bot_id] = []
                orders_by_bot[bot_id].append(order)

            # Process orders by bot
            processed_count = 0
            for bot_id, orders in orders_by_bot.items():
                try:
                    # Get bot info
                    bot = db.query(TradingBot).filter(TradingBot.id == bot_id).first()
                    if not bot or bot.status_id != 1:  # Skip inactive bots
                        continue

                    print(f"🤖 Processing {len(orders)} stale orders for bot {bot.uuid}")

                    # Create payload for order monitoring lambda
                    payload = {
                        "multiValueQueryStringParameters": None,
                        "headers": {
                            "x-api-key": os.environ['AI_API_KEY'],
                            "access-token": None,
                            "refresh-token": None,
                            "account-id": None
                        },
                        "pathParameters": None,
                        "requestContext": {
                            "httpMethod": None,
                            "resourcePath": None
                        },
                        "body": json.dumps({
                            "bot_uuid": bot.uuid,
                            "stale_order_uuids": [order.uuid for order in orders]
                        }),
                        "stageVariables": {
                            "stage": os.environ['STAGE']
                        }
                    }

                    # Call the order monitoring lambda
                    lambdaName = os.environ.get('ORDER_MONITOR_LAMBDA', 'thriving-dev-order_monitor')
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='Event',
                        Payload=json.dumps(payload)
                    )

                    processed_count += len(orders)

                    # Add throttling for Robinhood
                    if bot.bot_type_id == 3:  # Robinhood
                        time.sleep(2)

                except Exception as e:
                    print(f"❌ Error processing orders for bot {bot_id}: {str(e)}")
                    continue

            print(f"✅ Sell order monitoring completed. Processed {processed_count} orders across {len(orders_by_bot)} bots")
    except Exception as e:
        print(f"❌ MonitorSellOrders cron error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.lambda_function(name='order_monitor')
def orderMonitorLambda(event, context):
    """
    Lambda function to handle individual bot order monitoring.
    Cancels stale orders and places new ones with updated prices.
    """
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.serializer import BotSerializer
    from chalicelib.Bots.models import Trades

    print("🔧 Order monitor lambda started")

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db

            body = json.loads(event['body'])
            bot_uuid = body['bot_uuid']
            stale_order_uuids = body['stale_order_uuids']

            print(f"🎯 Processing {len(stale_order_uuids)} stale orders for bot {bot_uuid}")

            processed_orders = 0

            for order_uuid in stale_order_uuids:
                try:
                    # Get the order
                    order = db.query(Trades).filter(Trades.uuid == order_uuid).first()
                    if not order or order.status_id != 4:  # Skip if not pending
                        continue

                    print(f"📋 Processing stale order: {order.symbol} - {order.uuid}")

                    # Cancel the existing order
                    cancel_result = BotSerializer.cancel_order(order_uuid, app)
                    if cancel_result.get('success'):
                        print(f"❌ Cancelled stale order: {order.symbol}")

                        # Place a new order with current market price
                        new_order_data = {
                            "symbol": order.symbol,
                            "account_id": order.account_id,
                            "action": "sell",
                            "bot_uuid": bot_uuid,
                            "quantity": order.quantity
                            # Price will be determined by current market price in sell method
                        }

                        # Place new sell order
                        sell_result = BotSerializer.sell(new_order_data, app, DT_LIMIT=False)
                        if sell_result.get('success'):
                            print(f"✅ Placed new sell order: {order.symbol}")
                            processed_orders += 1
                        else:
                            print(f"⚠️ Failed to place new sell order: {order.symbol}")
                    else:
                        print(f"⚠️ Failed to cancel stale order: {order.symbol}")

                except Exception as e:
                    print(f"❌ Error processing order {order_uuid}: {str(e)}")
                    continue

            print(f"✅ Order monitor completed. Processed {processed_orders} orders")
            return {"success": True, "processed_orders": processed_orders}
    except Exception as e:
        print(f"❌ OrderMonitorLambda lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################
########  Market Sentiment Analysis (Every 5 minutes)
#################################################
@app.schedule(Cron('*/15', '*', '?', '*', '*', '*'))
def marketSentimentAnalysis(event):
    """
    Market sentiment analysis cron job that runs every 15 minutes.
    Monitors overall market sentiment using AlphaAdvantage news API.
    Tracks keywords like: tariffs, Trump, interest rates, inflation, Fed, recession, etc.
    OPTIMIZED for minimal database connections with proper cleanup.
    """
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import unixTimeNow
    from chalicelib.Articles.models import Article
    from chalicelib.MarketSentiment.models import MarketSentiment
    import requests
    import json
    import re
    from datetime import datetime, timedelta

    print("📊 Starting market sentiment analysis (optimized)...")

    try:
        with get_db_session_with_retry() as db:
            # Market-affecting keywords to monitor
            market_keywords = [
                'tariffs', 'interest rates', 'inflation', 'federal reserve', 'fed',
                'recession', 'war', 'gdp', 'unemployment', 'jobs report', 'cpi', 'ppi',
                'monetary policy', 'fiscal policy', 'trade war', 'china trade',
                'oil prices', 'energy crisis', 'supply chain', 'geopolitical',
                'sanctions', 'election', 'biden', 'congress', 'senate',
                'debt ceiling', 'government shutdown', 'stimulus', 'bailout',
                'bank crisis', 'credit crunch', 'market crash', 'volatility',
                'earnings season', 'guidance', 'outlook', 'forecast', 'bear market',
                'bull market'
            ]

            # Collect news from multiple sources
            all_news_articles = []
            source_stats = {}

            # 1. AlphaAdvantage News Sentiment API
            try:
                print("🔍 Fetching news from AlphaAdvantage...")
                api_key = os.environ.get('AA_API_KEY', 'VKV7S2P3F4QD1FMU')
                time_from = (datetime.now() - timedelta(hours=3)).strftime('%Y%m%dT%H%M')

                url = f"https://www.alphavantage.co/query?function=NEWS_SENTIMENT&time_from={time_from}&apikey={api_key}&sort=LATEST&limit=100"
                response = requests.get(url, timeout=30)
                data = response.json()

                if 'feed' in data:
                    aa_articles = data['feed']
                    for article in aa_articles:
                        article['source_api'] = 'AlphaAdvantage'
                    all_news_articles.extend(aa_articles)
                    source_stats['AlphaAdvantage'] = len(aa_articles)
                    print(f"✅ AlphaAdvantage: {len(aa_articles)} articles")
                else:
                    print(f"⚠️ AlphaAdvantage API issue: {data}")
                    source_stats['AlphaAdvantage'] = 0
            except Exception as e:
                print(f"❌ AlphaAdvantage error: {e}")
                source_stats['AlphaAdvantage'] = 0

        # 2. NewsAPI.org (Free tier: 1000 requests/month)
        try:
            print("🔍 Fetching news from NewsAPI...")
            # You can get a free API key from https://newsapi.org/
            newsapi_key = "********************************"

            if newsapi_key != 'demo':
                # Financial and business news sources
                sources = 'reuters,bloomberg,cnbc,financial-times,the-wall-street-journal,business-insider'
                url = f"https://newsapi.org/v2/everything?sources={sources}&q=market OR economy OR inflation OR fed OR interest rates OR tariffs&sortBy=publishedAt&pageSize=50&apiKey={newsapi_key}"

                response = requests.get(url, timeout=30)
                data = response.json()

                if data.get('status') == 'ok' and 'articles' in data:
                    newsapi_articles = []
                    for article in data['articles']:
                        # Convert NewsAPI format to our standard format
                        converted_article = {
                            'title': article.get('title', ''),
                            'summary': article.get('description', ''),
                            'url': article.get('url', ''),
                            'source': article.get('source', {}).get('name', 'NewsAPI'),
                            'time_published': article.get('publishedAt', ''),
                            'overall_sentiment_score': 0.0,  # Will be analyzed by our keyword system
                            'source_api': 'NewsAPI'
                        }
                        newsapi_articles.append(converted_article)

                    all_news_articles.extend(newsapi_articles)
                    source_stats['NewsAPI'] = len(newsapi_articles)
                    print(f"✅ NewsAPI: {len(newsapi_articles)} articles")
                else:
                    print(f"⚠️ NewsAPI issue: {data.get('message', 'Unknown error')}")
                    source_stats['NewsAPI'] = 0
            else:
                print("⚠️ NewsAPI key not configured (using demo)")
                source_stats['NewsAPI'] = 0
        except Exception as e:
            print(f"❌ NewsAPI error: {e}")
            source_stats['NewsAPI'] = 0

        # 3. Guardian API (Free with registration)
        try:
            print("🔍 Fetching news from The Guardian...")
            guardian_key = "************************************"  # Get free key from https://open-platform.theguardian.com/

            if guardian_key != 'test':
                # Search for financial and economic news
                query = 'economy OR "financial markets" OR inflation OR "federal reserve" OR tariffs OR "interest rates"'
                url = f"https://content.guardianapis.com/search?q={query}&section=business&show-fields=headline,trailText,webUrl&page-size=30&api-key={guardian_key}"

                response = requests.get(url, timeout=30)
                data = response.json()

                if data.get('response', {}).get('status') == 'ok':
                    guardian_articles = []
                    for article in data['response']['results']:
                        fields = article.get('fields', {})
                        converted_article = {
                            'title': fields.get('headline', article.get('webTitle', '')),
                            'summary': fields.get('trailText', ''),
                            'url': article.get('webUrl', ''),
                            'source': 'The Guardian',
                            'time_published': article.get('webPublicationDate', ''),
                            'overall_sentiment_score': 0.0,
                            'source_api': 'Guardian'
                        }
                        guardian_articles.append(converted_article)

                    all_news_articles.extend(guardian_articles)
                    source_stats['Guardian'] = len(guardian_articles)
                    print(f"✅ Guardian: {len(guardian_articles)} articles")
                else:
                    print(f"⚠️ Guardian API issue: {data}")
                    source_stats['Guardian'] = 0
            else:
                print("⚠️ Guardian API key not configured")
                source_stats['Guardian'] = 0
        except Exception as e:
            print(f"❌ Guardian error: {e}")
            source_stats['Guardian'] = 0

        # 4. RSS Feeds from major financial news sources (Free)
        try:
            print("🔍 Fetching RSS feeds...")
            import feedparser

            rss_feeds = [
                ('Reuters Business', 'https://feeds.reuters.com/reuters/businessNews'),
                ('BBC Business', 'https://feeds.bbci.co.uk/news/business/rss.xml'),
                ('CNN Business', 'http://rss.cnn.com/rss/money_latest.rss'),
                ('MarketWatch', 'https://feeds.marketwatch.com/marketwatch/realtimeheadlines/'),
                ('Yahoo Finance', 'https://feeds.finance.yahoo.com/rss/2.0/headline')
            ]

            rss_articles = []
            for feed_name, feed_url in rss_feeds:
                try:
                    feed = feedparser.parse(feed_url)
                    for entry in feed.entries[:10]:  # Limit to 10 articles per feed
                        converted_article = {
                            'title': entry.get('title', ''),
                            'summary': entry.get('summary', entry.get('description', '')),
                            'url': entry.get('link', ''),
                            'source': feed_name,
                            'time_published': entry.get('published', ''),
                            'overall_sentiment_score': 0.0,
                            'source_api': 'RSS'
                        }
                        rss_articles.append(converted_article)
                except Exception as e:
                    print(f"⚠️ RSS feed error for {feed_name}: {e}")
                    continue

            all_news_articles.extend(rss_articles)
            source_stats['RSS_Feeds'] = len(rss_articles)
            print(f"✅ RSS Feeds: {len(rss_articles)} articles")

        except ImportError:
            print("⚠️ feedparser not installed - skipping RSS feeds")
            source_stats['RSS_Feeds'] = 0
        except Exception as e:
            print(f"❌ RSS feeds error: {e}")
            source_stats['RSS_Feeds'] = 0

        if not all_news_articles:
            print("❌ No news articles retrieved from any source")
            return {"success": False, "message": "No news data available from any source"}

        print(f"📰 Total articles collected: {len(all_news_articles)} from {len([k for k, v in source_stats.items() if v > 0])} sources")
        print(f"📊 Source breakdown: {source_stats}")

        news_articles = all_news_articles

        # Initialize sentiment tracking
        total_sentiment_score = 0.0
        keyword_mentions = {}
        article_count = 0
        positive_articles = 0
        negative_articles = 0
        neutral_articles = 0

        # Import sentiment analysis function
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

        # Process each article
        for article in news_articles:
            try:
                title = article.get('title', '').lower()
                summary = article.get('summary', '').lower()
                content = f"{title} {summary}"

                # Get overall sentiment score
                overall_sentiment = article.get('overall_sentiment_score', 0)

                # If no sentiment score (non-AlphaAdvantage sources), calculate it
                if overall_sentiment == 0 and article.get('source_api') != 'AlphaAdvantage':
                    overall_sentiment = MarketSentimentSerializer.analyze_sentiment_from_keywords(content)

                if overall_sentiment != 0:
                    overall_sentiment = float(overall_sentiment)
                    total_sentiment_score += overall_sentiment
                    article_count += 1

                    # Categorize sentiment
                    if overall_sentiment > 0.1:
                        positive_articles += 1
                    elif overall_sentiment < -0.1:
                        negative_articles += 1
                    else:
                        neutral_articles += 1

                # Check for market-affecting keywords
                for keyword in market_keywords:
                    if keyword in content:
                        if keyword not in keyword_mentions:
                            keyword_mentions[keyword] = {
                                'count': 0,
                                'sentiment_sum': 0.0,
                                'articles': []
                            }
                        keyword_mentions[keyword]['count'] += 1
                        keyword_mentions[keyword]['sentiment_sum'] += overall_sentiment
                        keyword_mentions[keyword]['articles'].append({
                            'title': article.get('title', ''),
                            'sentiment': overall_sentiment,
                            'source': article.get('source', ''),
                            'url': article.get('url', '')
                        })

            except Exception as e:
                print(f"⚠️ Error processing article: {e}")
                continue

        # Define high-impact keywords that should have increased weighting
        high_impact_keywords = {
            'inflation': 3.0,      # 3x weight - inflation is critical for market sentiment
            'fed': 2.5,            # 2.5x weight - Federal Reserve decisions are major market movers
            'federal reserve': 2.5, # 2.5x weight - Same as 'fed'
            'interest rates': 2.5   # 2.5x weight - Interest rate changes significantly impact markets
        }

        # Calculate keyword sentiment scores with enhanced weighting for critical keywords
        keyword_sentiment_scores = {}
        total_keyword_impact = 0.0
        total_keyword_weight = 0.0

        for keyword, data in keyword_mentions.items():
            if data['count'] > 0:
                avg_sentiment = data['sentiment_sum'] / data['count']

                # Apply enhanced weighting for high-impact keywords
                weight_multiplier = high_impact_keywords.get(keyword, 1.0)
                base_weight = min(data['count'], 10)  # Cap base weight at 10 mentions
                weighted_count = base_weight * weight_multiplier
                impact_score = avg_sentiment * weighted_count

                keyword_sentiment_scores[keyword] = {
                    'mentions': data['count'],
                    'average_sentiment': avg_sentiment,
                    'impact_score': impact_score,
                    'weight_multiplier': weight_multiplier,
                    'base_weight': base_weight
                }

                # Log high-impact keyword processing
                if weight_multiplier > 1.0:
                    print(f"🔥 High-impact keyword '{keyword}': {data['count']} mentions, "
                          f"sentiment {avg_sentiment:.3f}, base weight {base_weight}, "
                          f"weighted impact {impact_score:.3f} (x{weight_multiplier})")

                # Accumulate for weighted overall sentiment
                total_keyword_impact += impact_score
                total_keyword_weight += weighted_count

        # Calculate overall market sentiment with enhanced keyword weighting
        if article_count > 0:
            base_sentiment = total_sentiment_score / article_count

            # If we have significant keyword activity, blend with keyword sentiment
            if total_keyword_weight > 0:
                keyword_weighted_sentiment = total_keyword_impact / total_keyword_weight

                # Enhanced weighting: High-impact keywords can have up to 40% influence
                # This gives more influence to critical market-affecting terms like inflation, fed, interest rates
                keyword_influence = min(total_keyword_weight / 25.0, 0.4)  # Max 40% influence for high-impact keywords
                average_sentiment = (base_sentiment * (1 - keyword_influence)) + (keyword_weighted_sentiment * keyword_influence)

                print(f"📊 Enhanced sentiment calculation:")
                print(f"   Base sentiment: {base_sentiment:.6f}")
                print(f"   Keyword-weighted sentiment: {keyword_weighted_sentiment:.6f}")
                print(f"   Keyword influence: {keyword_influence:.3f} ({keyword_influence*100:.1f}%)")
                print(f"   Final blended sentiment: {average_sentiment:.6f}")
            else:
                average_sentiment = base_sentiment
                print(f"📊 No keyword influence, using base sentiment: {average_sentiment:.6f}")
        else:
            average_sentiment = 0.0

        # Determine market sentiment level using updated responsive thresholds
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
        sentiment_level = MarketSentimentSerializer._get_sentiment_level(average_sentiment)

        # Create market sentiment record
        current_time = unixTimeNow()

        market_sentiment = MarketSentiment(
            sentiment_score=average_sentiment,
            sentiment_level=sentiment_level,
            total_articles=article_count,
            positive_articles=positive_articles,
            negative_articles=negative_articles,
            neutral_articles=neutral_articles,
            keyword_mentions=json.dumps(keyword_sentiment_scores),
            top_keywords=json.dumps(list(sorted(keyword_mentions.keys(),
                                              key=lambda k: keyword_mentions[k]['count'],
                                              reverse=True)[:10])),
            analysis_timestamp=current_time,
            status_id=1  # Active status
        )

        db.add(market_sentiment)
        db.commit()

        # Log significant sentiment changes or keyword alerts
        alerts = []

        # Check for high-impact keywords
        high_impact_keywords = [k for k, v in keyword_sentiment_scores.items()
                               if abs(v['impact_score']) > 2.0]

        if high_impact_keywords:
            alerts.append(f"High-impact keywords detected: {', '.join(high_impact_keywords)}")

        # Check for extreme sentiment (updated threshold to be more responsive)
        if abs(average_sentiment) > 0.35:
            alerts.append(f"Extreme market sentiment detected: {sentiment_level} ({average_sentiment:.3f})")

        # Check for high keyword activity
        total_keyword_mentions = sum(data['count'] for data in keyword_mentions.values())
        if total_keyword_mentions > 20:
            alerts.append(f"High keyword activity: {total_keyword_mentions} mentions of market-affecting terms")

        # 🚨 TRIGGER EMERGENCY SELLS FOR SENTIMENT DROPS AND NEGATIVE SENTIMENT
        emergency_sells_triggered = 0

        # Check if we should trigger sells based on sentiment changes
        should_trigger_sells = False
        trigger_reason = ""

        # Get previous sentiment to check for drops
        try:
            from sqlalchemy import desc
            previous_sentiment = db.query(MarketSentiment).filter(
                MarketSentiment.status_id == 1,
                MarketSentiment.id != market_sentiment.id  # Exclude the current record we just created
            ).order_by(desc(MarketSentiment.analysis_timestamp)).first()

            previous_level = previous_sentiment.sentiment_level if previous_sentiment else "Unknown"

        except Exception as e:
            print(f"⚠️ Could not retrieve previous sentiment: {e}")
            previous_level = "Unknown"

        # Trigger conditions:
        # 1. Sentiment drops from Positive/Very Positive to Neutral or worse
        # 2. Sentiment drops from Neutral to Negative or worse
        # 3. Any Very Negative sentiment (original condition)

        if sentiment_level == "Very Negative":
            should_trigger_sells = True
            trigger_reason = "Very negative sentiment detected"
        elif sentiment_level in ["Negative", "Neutral"] and previous_level in ["Very Positive", "Positive"]:
            should_trigger_sells = True
            trigger_reason = f"Sentiment dropped from {previous_level} to {sentiment_level}"
        elif sentiment_level == "Negative" and previous_level == "Neutral":
            should_trigger_sells = True
            trigger_reason = f"Sentiment dropped from Neutral to Negative"

        if should_trigger_sells:
            try:
                print(f"🚨 MARKET SENTIMENT SELL TRIGGER: {trigger_reason} - Triggering emergency sells across all automations")

                # Log the market sentiment alert event for system tracking
                try:
                    from chalicelib.Logs.serializer import EventSerializer
                    from chalicelib.Utils.functions import ChaliceRequest

                    # Create a system event for the market sentiment alert
                    # This logs the overall sentiment detection, individual sell events are logged separately
                    negative_keywords_list = [k for k, v in keyword_sentiment_scores.items() if v['impact_score'] < -1.0]
                    top_negative_keywords = sorted(negative_keywords_list, key=lambda k: keyword_sentiment_scores[k]['impact_score'])[:5]

                    system_event_description = (
                        f"MARKET SENTIMENT ALERT: {trigger_reason} ({average_sentiment:.3f}). "
                        f"Previous sentiment: {previous_level}. Analyzed {article_count} articles. "
                        f"Emergency sell protocol initiated across all automations. "
                        f"Top negative keywords: {', '.join(top_negative_keywords[:3]) if top_negative_keywords else 'None'}."
                    )

                    # Create mock request for system event logging (no specific account)
                    mock_event = {"headers": {"x-api-key": "market_sentiment_system"}, "body": "{}"}
                    mock_request = ChaliceRequest(mock_event)
                    mock_request.db = db

                    class MockApp:
                        def __init__(self):
                            self.current_request = mock_request

                    mock_app = MockApp()

                    # Log system-wide market sentiment event (using account_id = 1 for system events)
                    EventSerializer.create(
                        account_id=1,  # System account for global events
                        event_type_id=3,  # System event type
                        description=system_event_description,
                        app=mock_app
                    )
                    print(f"📝 System event logged for market sentiment alert")

                except Exception as event_error:
                    print(f"⚠️ Failed to log system market sentiment event: {event_error}")
                    # Don't fail the sentiment analysis if event logging fails

                sell_result = MarketSentimentSerializer.trigger_sentiment_based_sells()
                if sell_result.get("success"):
                    emergency_sells_triggered = sell_result.get("sells_triggered", 0)
                    alerts.append(f"Emergency sells triggered: {emergency_sells_triggered} positions sold due to sentiment change ({trigger_reason})")
                    print(f"✅ Emergency sell trigger completed: {emergency_sells_triggered} sells initiated - {trigger_reason}")
                else:
                    alerts.append(f"Emergency sell trigger failed: {sell_result.get('error', 'Unknown error')}")
                    print(f"❌ Emergency sell trigger failed: {sell_result.get('error', 'Unknown error')}")

            except Exception as e:
                alerts.append(f"Error triggering emergency sells: {str(e)}")
                print(f"❌ Error triggering emergency sells: {str(e)}")
                import traceback
                traceback.print_exc()

        print(f"📊 Market Sentiment Analysis Complete:")
        print(f"   Overall Sentiment: {sentiment_level} ({average_sentiment:.3f})")
        print(f"   Articles Processed: {article_count}")
        print(f"   Sentiment Distribution: {positive_articles} positive, {negative_articles} negative, {neutral_articles} neutral")
        print(f"   Keyword Mentions: {len(keyword_mentions)} unique keywords, {total_keyword_mentions} total mentions")
        print(f"   Keyword Weight: {total_keyword_weight:.1f}, Impact: {total_keyword_impact:.3f}")

        if alerts:
            print(f"🚨 ALERTS:")
            for alert in alerts:
                print(f"   - {alert}")

        # Top 5 most mentioned keywords
        top_keywords = sorted(keyword_mentions.items(), key=lambda x: x[1]['count'], reverse=True)[:5]
        if top_keywords:
            print(f"🔥 Top Keywords:")
            for keyword, data in top_keywords:
                avg_sent = data['sentiment_sum'] / data['count'] if data['count'] > 0 else 0
                impact = keyword_sentiment_scores.get(keyword, {}).get('impact_score', 0)
                print(f"   - {keyword}: {data['count']} mentions (avg sentiment: {avg_sent:.3f}, impact: {impact:.3f})")

        # Show most negative impact keywords
        negative_keywords = sorted(
            [(k, v) for k, v in keyword_sentiment_scores.items() if v['impact_score'] < -1.0],
            key=lambda x: x[1]['impact_score']
        )[:3]
        if negative_keywords:
            print(f"⚠️ Most Negative Impact Keywords:")
            for keyword, data in negative_keywords:
                print(f"   - {keyword}: {data['mentions']} mentions, impact: {data['impact_score']:.3f}")

            return {
                "success": True,
                "sentiment_score": average_sentiment,
                "sentiment_level": sentiment_level,
                "articles_processed": article_count,
                "keyword_mentions": len(keyword_mentions),
                "alerts": alerts,
                "emergency_sells_triggered": emergency_sells_triggered,
                "news_sources": source_stats,
                "total_articles_collected": len(all_news_articles)
            }

    except Exception as e:
        print(f"❌ Error in market sentiment analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


@app.lambda_function(name='take_profit')
def takeProfitLambda(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import json

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            res = AutomationSerializer.profit_check(body['bot_uuid'], app)
            return res
    except Exception as e:
        print(f"❌ TakeProfitLambda lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########           Backtest Endpoints      ###################
#################################################################
@apiDecorator.protectedRoute('/portfolio/backtest', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def backtestAccountPortfolio():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = PortfolioSerializer.backtestPortfolio(app, DATA=data, BACKTEST_ID=data.get('backtest_id', None))
            return response
    except Exception as e:
        print(f"❌ BacktestAccountPortfolio endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/portfolio/backtest/enhanced', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def backtestAccountPortfolioEnhanced():
    """
    Enhanced backtest endpoint with new AI models and improved features
    Expected payload:
    {
        "initial_balance": 10000.00,
        "date_start": "2025-03-01",
        "date_end": "2025-05-22",
        "take_profit": 1.0,
        "account_uuid": "{{account_id}}",
        "interval": "daily"
    }
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body
            response = PortfolioSerializer.backtestPortfolioEnhanced(app, DATA=data)
            return response
    except Exception as e:
        print(f"❌ BacktestAccountPortfolioEnhanced endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


# @app.schedule(Cron('*/15', '*', '?', '*', '*', '*'))
# def checkBacktestSagemaker(event):
#     from chalicelib.Utils.database import conn
#     from chalicelib.Portfolios.models import Backtests
#     from chalicelib.Utils.functions import ChaliceRequest

#     context = {
#         "multiValueQueryStringParameters": None,
#         "headers": {
#             "x-api-key": os.environ['AI_API_KEY'],
#             "access-token": None,
#             "refresh-token": None,
#             "account-id": None
#         },
#         "pathParameters": None,
#         "requestContext": {
#             "httpMethod": None,
#             "resourcePath": None
#         },
#         "body": None,
#         "stageVariables": {
#             "stage": os.environ['STAGE']
#         },
#         "query_params": {
#             "timespan": "last_month"
#         }
#     }

#     app.current_request = ChaliceRequest(context)
#     app.current_request.db = conn()

#     ## Check to see if any backtests are pending and sagemaker is not running
#     db = conn()
#     backtest_pending = db.query(Backtests).filter(Backtests.status_id == 4).all()

#     ## Check if Sagemaker is running
#     client = boto3.client("sagemaker")
#     if len(backtest_pending) > 0:
#         try:
#             res = client.describe_endpoint(
#                 EndpointName="Backtesting"
#             )
#             if res['EndpointStatus'] != 'InService':
#                 return True

#             else:
#                 for b in backtest_pending:
#                     PortfolioSerializer.backtestPortfolio(app, BACKTEST_ID=b.id)

#         except Exception as e:
#             try:
#                 res = client.create_endpoint(
#                     EndpointName="Backtesting",
#                     EndpointConfigName="Backtest-AI-Config-2",
#                 )
#             except Exception as e:
#                 print(traceback.format_exc())
#                 return False

#     else:
#         ## Check if any backtests are running
#         backtests_running = db.query(Backtests).filter(Backtests.status_id == 14).all()
#         if len(backtests_running) > 0:
#             return True
#         else:
#             client = boto3.client("sagemaker")
#             try:
#                 res = client.delete_endpoint(
#                     EndpointName="Backtesting"
#                 )
#             except Exception as e:
#                 print(traceback.format_exc())
#             response = {}
#             response['success'] = True
#             response['endpoint'] = res
#             return response


@app.lambda_function(name='backtest_report')
def reportBacktest(event, context):
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request = ChaliceRequest(event)
            app.current_request.db = db
            body = json.loads(event['body'])
            res = PortfolioSerializer.backtestUpdateStatus(body, app)
    except Exception as e:
        print(f"❌ ReportBacktest lambda error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########           Test Endpoints      ###################
#################################################################
# NOTE: These test endpoints should be disabled or removed in production
# They are kept for development and debugging purposes only
# Consider using environment variables to conditionally enable them

# Test endpoint enablement check
def is_test_mode_enabled():
    """Check if test endpoints should be enabled based on environment"""
    stage = os.environ.get('STAGE', 'local')
    test_mode = os.environ.get('ENABLE_TEST_ENDPOINTS', 'false').lower()
    return stage == 'local' or test_mode == 'true'

def require_admin_or_test_mode():
    """Check if user has admin access or if we're in test mode"""
    if is_test_mode_enabled():
        return True

    # Check for admin access (implement based on your user system)
    request_user = getattr(app.current_request, 'requestUser', None)
    if request_user and request_user.get('role') == 'admin':
        return True

    return False

@apiDecorator.protectedRoute('/test/signal', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testSignals():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db

            # Production safety check
            if not is_test_mode_enabled():
                return {"success": False, "error": "Test endpoints are disabled in production"}

            data = app.current_request.json_body
            print(json.dumps(data, indent=2))
            from chalicelib.Bots.models import TradingBot
            from chalicelib.Utils.exceptions import APIException

            try:
                bot = db.query(TradingBot).filter(TradingBot.uuid == data['bot_uuid']).one()
            except:
                raise APIException("Unable to locate bot uuid")
            try:
                print(colored("Processing signal", "green"))
                response = AutomationSerializer.signal(bot, data, app)
            except:
                print(traceback.format_exc())
                raise APIException("Unable to process signal")
            return response
    except Exception as e:
        print(f"❌ TestSignals endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/ai/analysis/manual', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def manualTrigger():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Portfolios.serializer import PortfolioSerializer
            import requests, datetime

            ## Get symbols
            symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app)
            print(f"🔍 Manual analysis triggered for {len(symbols)} symbols: {symbols[:10]}{'...' if len(symbols) > 10 else ''}")

            if os.environ['STAGE'] != 'local':
                headers = {
                    "x-api-key": os.environ['AI_API_KEY']
                }
                date = datetime.datetime.now()
                date = date.strftime("%Y-%m-%d")
                print(f"🏠 Local environment - making direct API calls to {os.environ['AI_URL']}")

                for i, s in enumerate(symbols):
                    if i > 10:
                        break
                    body = {
                        "date": date,
                        "market_session": "market_close"
                    }
                    print(f"📡 Analyzing symbol {i+1}/{len(symbols)}: {s}")
                    try:
                        response = requests.post(
                            url=f"{os.environ['AI_URL']}/symbol/{s}/analyze",
                            json=body,
                            headers=headers,
                            timeout=120  # 2 minute timeout
                        )
                        print(f"   Response: {response.status_code} - {response.text[:100]}...")
                    except Exception as e:
                        print(f"   ❌ Failed to analyze {s}: {e}")
            else:
                ## Send symbols to queue in batches to prevent overwhelming SageMaker
                print(f"☁️  Production environment - invoking Lambda functions in batches")

                # Process symbols in batches of 100 with shorter delays for 500-thread SageMaker
                batch_size = 100
                batch_delay = 5  # 5 seconds between batches
                total_batches = (len(symbols) - 1) // batch_size + 1
                processed_count = 0

                import time
                for batch_num in range(total_batches):
                    start_idx = batch_num * batch_size
                    end_idx = min(start_idx + batch_size, len(symbols))
                    batch_symbols = symbols[start_idx:end_idx]

                    print(f"📦 Processing batch {batch_num + 1}/{total_batches}: {len(batch_symbols)} symbols")

                    for i, s in enumerate(batch_symbols):
                        
                        symbol_num = start_idx + i + 1
                        print(f"🚀 Queuing symbol {symbol_num}/{len(symbols)}: {s}")
                        body = {
                            "symbol": s,
                            "market_session": "market_close"
                        }
                        payload = {
                            "multiValueQueryStringParameters": None,
                            "headers": {
                                "x-api-key": os.environ['AI_API_KEY'],
                                "access-token": None,
                                "refresh-token": None,
                                "account-id": None
                            },
                            "pathParameters": None,
                            "requestContext": {
                                "httpMethod": None,
                                "resourcePath": None
                            },
                            "body": None,
                            "stageVariables": {
                                "stage": os.environ['STAGE']
                            }
                        }
                        payload['body'] = json.dumps(body)

                        ## Call the lambda
                        lambdaName = os.environ['ANALYZE_SYMBOL_LAMBDA']
                        try:
                            lambda_response = boto3.client('lambda').invoke(
                                FunctionName=lambdaName,
                                InvocationType='Event',
                                Payload=json.dumps(payload)
                            )
                            print(f"   ✅ Lambda invoked for {s}: {lambda_response.get('StatusCode', 'unknown')}")
                            processed_count += 1
                        except Exception as e:
                            print(f"   ❌ Failed to invoke Lambda for {s}: {e}")

                    # Add delay between batches (except for the last batch)
                    if batch_num < total_batches - 1:
                        print(f"⏳ Waiting {batch_delay} seconds before next batch...")
                        time.sleep(batch_delay)

                print(f"✅ Batch processing completed: {processed_count} symbols queued in {total_batches} batches")
            print(f"✅ Manual analysis trigger completed for {len(symbols)} symbols")
            return {
                "success": True,
                "symbols_processed": len(symbols),
                "symbols": symbols[:10] if len(symbols) <= 10 else symbols[:10] + ["..."]
            }
    except Exception as e:
        print(f"❌ ManualTrigger endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/ai/circuit-breaker/status', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getCircuitBreakerStatus():
    """Get SageMaker circuit breaker status via AI processing service"""
    try:
        import requests

        headers = {
            "x-api-key": os.environ['AI_API_KEY']
        }

        response = requests.get(
            url=f"{os.environ['AI_URL']}/sagemaker/circuit-breaker/status",
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'AI service returned {response.status_code}: {response.text}'
            }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


@apiDecorator.protectedRoute('/ai/circuit-breaker/reset', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def resetCircuitBreaker():
    """Reset SageMaker circuit breaker via AI processing service"""
    try:
        import requests

        headers = {
            "x-api-key": os.environ['AI_API_KEY']
        }

        response = requests.post(
            url=f"{os.environ['AI_URL']}/sagemaker/circuit-breaker/reset",
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            return response.json()
        else:
            return {
                'success': False,
                'error': f'AI service returned {response.status_code}: {response.text}'
            }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


@apiDecorator.protectedRoute('/test/balance/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testUpdateBalance(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            automation, total_balance, available_cash = BotSerializer._update_balance(bot_uuid, app)
            res = AccountSerializer.calculateBalance(automation.account_uuid, app)
            return res
    except Exception as e:
        print(f"❌ TestUpdateBalance endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/test/take_profit/{bot_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testTakeProfit(bot_uuid):
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            res = AutomationSerializer.profit_check(bot_uuid, app)
            return res
    except Exception as e:
        print(f"❌ TestTakeProfit endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/sell_order_monitor', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testSellOrderMonitor():
    """Test endpoint to manually trigger sell order monitoring"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.models import TradingBot, Trades
            from chalicelib.Utils.functions import unixTimeNow

            print("🧪 Testing sell order monitoring...")

            # Calculate 5 minutes ago timestamp
            five_minutes_ago = unixTimeNow() - (5 * 60)

            # Find pending sell orders older than 5 minutes
            stale_orders = db.query(Trades).filter(
                Trades.status_id == 4,  # Pending status
                Trades.action_type_id.in_([2, 8]),  # Sell orders
                Trades.order_time <= five_minutes_ago  # Older than 5 minutes
            ).limit(5).all()  # Limit to 5 for testing

            if not stale_orders:
                return {
                    "success": True,
                    "message": "No stale sell orders found for testing",
                    "stale_orders_count": 0
                }

            print(f"🔍 Found {len(stale_orders)} stale sell orders for testing")

            # Process a few orders for testing
            processed_orders = []
            for order in stale_orders[:3]:  # Process max 3 for testing
                try:
                    # Get bot info
                    bot = db.query(TradingBot).filter(TradingBot.id == order.trading_bot_id).first()
                    if not bot or bot.status_id != 1:
                        continue

                    order_info = {
                        "order_uuid": order.uuid,
                        "symbol": order.symbol,
                        "bot_uuid": bot.uuid,
                        "age_minutes": (unixTimeNow() - order.order_time) / 60,
                        "status": "processed"
                    }

                    # For testing, we'll just log what would happen
                    print(f"📋 Would process: {order.symbol} - Age: {order_info['age_minutes']:.1f} minutes")
                    processed_orders.append(order_info)

                except Exception as e:
                    print(f"❌ Error processing test order {order.uuid}: {str(e)}")
                    continue

            return {
                "success": True,
                "message": f"Sell order monitoring test completed",
                "stale_orders_found": len(stale_orders),
                "processed_orders": processed_orders,
                "note": "This is a test endpoint - no actual orders were cancelled/replaced"
            }
    except Exception as e:
        print(f"❌ TestSellOrderMonitor endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/premarket_take_profit', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testPremarketTakeProfit():
    """Test endpoint to manually trigger premarket take profit check"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.models import TradingBot, Trades
            from chalicelib.Portfolios.models import AccountPortfolio

            print("🌅 Testing premarket take profit...")

            # Get accounts with take profit enabled
            acctPreferences = db.query(AccountPortfolio).filter(AccountPortfolio.take_profit == 1).all()
            accts = [x.account_id for x in acctPreferences]
            automations = db.query(TradingBot).filter(TradingBot.status_id == 1, TradingBot.account_id.in_(accts)).limit(3).all()

            if not automations:
                return {
                    "success": True,
                    "message": "No automations with take profit enabled found",
                    "automations_count": 0
                }

            print(f"🤖 Found {len(automations)} automations with take profit enabled")

            # Process a few automations for testing
            processed_automations = []
            for automation in automations:
                try:
                    automation_info = {
                        "bot_uuid": automation.uuid,
                        "account_id": automation.account_id,
                        "bot_type": automation.bot_type_id,
                        "status": "would_process"
                    }

                    # For testing, we'll just log what would happen
                    print(f"🎯 Would process take profit for bot: {automation.uuid}")
                    processed_automations.append(automation_info)

                except Exception as e:
                    print(f"❌ Error processing test automation {automation.uuid}: {str(e)}")
                    continue

            return {
                "success": True,
                "message": f"Premarket take profit test completed",
                "automations_found": len(automations),
                "processed_automations": processed_automations,
                "note": "This is a test endpoint - no actual take profit checks were performed"
            }
    except Exception as e:
        print(f"❌ TestPremarketTakeProfit endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

#################################################
########  Market Sentiment API Endpoints
#################################################

@apiDecorator.protectedRoute('/market/sentiment/current', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getCurrentMarketSentiment():
    """Get the current market sentiment analysis"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            return MarketSentimentSerializer.get_current_sentiment()
    except Exception as e:
        print(f"❌ GetCurrentMarketSentiment endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/market/sentiment/history', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getMarketSentimentHistory():
    """Get market sentiment history"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            hours_back = int(app.current_request.query_params.get('hours', 24))
            return MarketSentimentSerializer.get_sentiment_history(hours_back)
    except Exception as e:
        print(f"❌ GetMarketSentimentHistory endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/market/sentiment/keywords', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getKeywordTrends():
    """Get keyword mention trends"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            keyword = app.current_request.query_params.get('keyword')
            hours_back = int(app.current_request.query_params.get('hours', 24))
            return MarketSentimentSerializer.get_keyword_trends(keyword, hours_back)
    except Exception as e:
        print(f"❌ GetKeywordTrends endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/market/sentiment/alerts', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getMarketSentimentAlerts():
    """Get active market sentiment alerts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            return MarketSentimentSerializer.get_active_alerts()
    except Exception as e:
        print(f"❌ GetMarketSentimentAlerts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/market/sentiment/alerts/{alert_uuid}/acknowledge', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def acknowledgeMarketSentimentAlert(alert_uuid):
    """Acknowledge a market sentiment alert"""
    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
    body = app.current_request.json_body or {}
    acknowledged_by = body.get('acknowledged_by')
    return MarketSentimentSerializer.acknowledge_alert(alert_uuid, acknowledged_by)

@apiDecorator.protectedRoute('/market/sentiment/summary', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getMarketSentimentSummary():
    """Get comprehensive market sentiment summary"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            hours_back = int(app.current_request.query_params.get('hours', 24))
            return MarketSentimentSerializer.get_sentiment_summary(hours_back)
    except Exception as e:
        print(f"❌ GetMarketSentimentSummary endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/market/sentiment/impact', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getMarketImpactScore():
    """Get market impact score based on sentiment"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            return MarketSentimentSerializer.get_market_impact_score()
    except Exception as e:
        print(f"❌ GetMarketImpactScore endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/market_sentiment', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testMarketSentiment():
    """Test endpoint to manually trigger market sentiment analysis"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.models import MarketSentiment
            from chalicelib.MarketSentiment.enhanced_sentiment import EnhancedMarketSentiment
            from datetime import datetime

            print("🧪 Testing enhanced market sentiment analysis...")

            # Test the enhanced sentiment analysis
            try:
                enhanced_result = EnhancedMarketSentiment.analyze_comprehensive_market_sentiment()
                print(f"✅ Enhanced sentiment analysis result: {enhanced_result}")

                if enhanced_result.get('success'):
                    print(f"📊 Market Condition: {enhanced_result['market_condition']}")
                    print(f"📈 Final Score: {enhanced_result['final_score']}")
                    print(f"🎯 Confidence: {enhanced_result['confidence_level']}")

                    # Show component breakdown
                    if 'components' in enhanced_result:
                        print("🔍 Component Breakdown:")
                        for component, data in enhanced_result['components'].items():
                            if isinstance(data, dict) and 'score' in data:
                                print(f"  - {component}: {data['score']:.3f}")

            except Exception as e:
                print(f"⚠️ Enhanced analysis failed: {str(e)}")
                enhanced_result = {"success": False, "error": str(e)}

            # Get recent sentiment records
            recent_sentiments = db.query(MarketSentiment).filter(
                MarketSentiment.status_id == 1
            ).order_by(MarketSentiment.analysis_timestamp.desc()).limit(5).all()

            if not recent_sentiments:
                return {
                    "success": True,
                    "message": "No market sentiment data found - cron job may not have run yet",
                    "recent_analyses": 0,
                    "enhanced_analysis": enhanced_result
                }

            sentiment_data = []
            for sentiment in recent_sentiments:
                sentiment_data.append({
                    "timestamp": sentiment.analysis_timestamp,
                    "datetime": datetime.fromtimestamp(sentiment.analysis_timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                    "sentiment_score": float(sentiment.sentiment_score),
                    "sentiment_level": sentiment.sentiment_level,
                    "total_articles": sentiment.total_articles,
                    "positive_articles": sentiment.positive_articles,
                    "negative_articles": sentiment.negative_articles,
                    "neutral_articles": sentiment.neutral_articles,
                    "alerts": {
                        "high_impact": sentiment.high_impact_alert,
                        "keyword_alert": sentiment.keyword_alert,
                        "extreme_sentiment": sentiment.extreme_sentiment_alert
                    }
                })

            return {
                "success": True,
                "message": f"Market sentiment test completed - found {len(recent_sentiments)} recent analyses",
                "recent_analyses": len(recent_sentiments),
                "sentiment_data": sentiment_data,
                "latest_sentiment": {
                    "score": float(recent_sentiments[0].sentiment_score),
                    "level": recent_sentiments[0].sentiment_level,
                    "articles_analyzed": recent_sentiments[0].total_articles
                }
            }
    except Exception as e:
        print(f"❌ TestMarketSentiment endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/market_sentiment_trigger', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testMarketSentimentTrigger():
    """Test endpoint to manually trigger market sentiment analysis (simulates cron job)"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            print("🧪 Manually triggering market sentiment analysis...")

            # Import required modules
            from chalicelib.Utils.functions import unixTimeNow
            from chalicelib.MarketSentiment.models import MarketSentiment
            import requests
            import json
            from datetime import datetime, timedelta

        try:
            # Market-affecting keywords to monitor
            market_keywords = [
                'tariffs', 'trump', 'interest rates', 'inflation', 'federal reserve', 'fed',
                'recession', 'gdp', 'unemployment', 'jobs report', 'cpi', 'ppi',
                'monetary policy', 'fiscal policy', 'trade war', 'china trade',
                'oil prices', 'energy crisis', 'supply chain', 'geopolitical'
            ]

            print("🔍 Fetching news from multiple sources...")

            # Initialize collections
            all_articles = []
            source_stats = {}

            # 1. AlphaAdvantage News Sentiment API (Primary source with sentiment scores)
            try:
                print("🔍 Fetching news from AlphaAdvantage...")
                api_key = os.environ.get('AA_API_KEY', 'VKV7S2P3F4QD1FMU')
                time_from = (datetime.now() - timedelta(hours=3)).strftime('%Y%m%dT%H%M')

                url = f"https://www.alphavantage.co/query?function=NEWS_SENTIMENT&time_from={time_from}&apikey={api_key}&sort=LATEST&limit=50"
                response = requests.get(url, timeout=30)
                data = response.json()

                if 'feed' in data:
                    articles = data['feed']
                    for article in articles:
                        article['source_api'] = 'AlphaAdvantage'
                        article['has_sentiment_score'] = True
                    all_articles.extend(articles)
                    source_stats['AlphaAdvantage'] = len(articles)
                    print(f"📰 Retrieved {len(articles)} articles from AlphaAdvantage")
                else:
                    print(f"⚠️ AlphaAdvantage API response: {data}")
                    source_stats['AlphaAdvantage'] = 0

            except Exception as e:
                print(f"⚠️ Error fetching from AlphaAdvantage: {e}")
                source_stats['AlphaAdvantage'] = 0

            # 2. NewsAPI.org (Free tier: 1000 requests/month)
            try:
                print("🔍 Fetching news from NewsAPI...")
                newsapi_key = "********************************"

                if newsapi_key != 'demo':
                    # Financial and business news sources
                    sources = 'reuters,bloomberg,cnbc,financial-times,the-wall-street-journal,business-insider'
                    url = f"https://newsapi.org/v2/everything?sources={sources}&q=market OR economy OR inflation OR fed OR interest rates OR tariffs&sortBy=publishedAt&pageSize=50&apiKey={newsapi_key}"

                    response = requests.get(url, timeout=30)
                    data = response.json()

                    if data.get('status') == 'ok' and 'articles' in data:
                        articles = data['articles']
                        for article in articles:
                            # Convert NewsAPI format to our standard format
                            standardized_article = {
                                'title': article.get('title', ''),
                                'summary': article.get('description', ''),
                                'url': article.get('url', ''),
                                'time_published': article.get('publishedAt', ''),
                                'source_api': 'NewsAPI',
                                'has_sentiment_score': False,
                                'source': {'name': article.get('source', {}).get('name', 'Unknown')}
                            }
                            all_articles.append(standardized_article)
                        source_stats['NewsAPI'] = len(articles)
                        print(f"📰 Retrieved {len(articles)} articles from NewsAPI")
                    else:
                        print(f"⚠️ NewsAPI response: {data}")
                        source_stats['NewsAPI'] = 0
                else:
                    print("⚠️ NewsAPI key not configured")
                    source_stats['NewsAPI'] = 0

            except Exception as e:
                print(f"⚠️ Error fetching from NewsAPI: {e}")
                source_stats['NewsAPI'] = 0

            # 3. Guardian API (Free with registration)
            try:
                print("🔍 Fetching news from The Guardian...")
                guardian_key = "************************************"

                if guardian_key != 'test':
                    # Search for financial and economic news
                    query = 'economy OR "financial markets" OR inflation OR "federal reserve" OR tariffs OR "interest rates"'
                    url = f"https://content.guardianapis.com/search?q={query}&section=business&show-fields=headline,trailText,webUrl&page-size=30&api-key={guardian_key}"

                    response = requests.get(url, timeout=30)
                    data = response.json()

                    if data.get('response', {}).get('status') == 'ok':
                        articles = data['response']['results']
                        for article in articles:
                            # Convert Guardian format to our standard format
                            standardized_article = {
                                'title': article.get('webTitle', ''),
                                'summary': article.get('fields', {}).get('trailText', ''),
                                'url': article.get('webUrl', ''),
                                'time_published': article.get('webPublicationDate', ''),
                                'source_api': 'Guardian',
                                'has_sentiment_score': False,
                                'source': {'name': 'The Guardian'}
                            }
                            all_articles.append(standardized_article)
                        source_stats['Guardian'] = len(articles)
                        print(f"📰 Retrieved {len(articles)} articles from The Guardian")
                    else:
                        print(f"⚠️ Guardian API response: {data}")
                        source_stats['Guardian'] = 0
                else:
                    print("⚠️ Guardian API key not configured")
                    source_stats['Guardian'] = 0

            except Exception as e:
                print(f"⚠️ Error fetching from Guardian: {e}")
                source_stats['Guardian'] = 0

            # 4. RSS Feeds (Free, reliable backup sources)
            try:
                print("🔍 Fetching news from RSS feeds...")
                import feedparser

                rss_feeds = [
                    'https://feeds.reuters.com/reuters/businessNews',
                    'https://feeds.bbci.co.uk/news/business/rss.xml',
                    'https://rss.cnn.com/rss/money_latest.rss'
                ]

                rss_articles = []
                for feed_url in rss_feeds:
                    try:
                        feed = feedparser.parse(feed_url)
                        for entry in feed.entries[:10]:  # Limit to 10 per feed
                            standardized_article = {
                                'title': entry.get('title', ''),
                                'summary': entry.get('summary', ''),
                                'url': entry.get('link', ''),
                                'time_published': entry.get('published', ''),
                                'source_api': 'RSS',
                                'has_sentiment_score': False,
                                'source': {'name': feed.feed.get('title', 'RSS Feed')}
                            }
                            rss_articles.append(standardized_article)
                    except Exception as e:
                        print(f"⚠️ Error parsing RSS feed {feed_url}: {e}")

                all_articles.extend(rss_articles)
                source_stats['RSS'] = len(rss_articles)
                print(f"📰 Retrieved {len(rss_articles)} articles from RSS feeds")

            except Exception as e:
                print(f"⚠️ Error fetching RSS feeds: {e}")
                source_stats['RSS'] = 0

            print(f"📊 Total articles collected: {len(all_articles)}")
            print(f"📊 Source breakdown: {source_stats}")

            # Process all articles for sentiment analysis
            total_sentiment_score = 0.0
            keyword_mentions = {}
            article_count = 0
            positive_articles = 0
            negative_articles = 0
            neutral_articles = 0

            for article in all_articles:
                try:
                    title = article.get('title', '').lower()
                    summary = article.get('summary', '').lower()
                    content = f"{title} {summary}"

                    # Get sentiment score (use AlphaAdvantage scores when available, estimate for others)
                    if article.get('has_sentiment_score') and article.get('overall_sentiment_score'):
                        overall_sentiment = float(article.get('overall_sentiment_score', 0))
                    else:
                        # Simple keyword-based sentiment estimation for non-AlphaAdvantage sources
                        positive_words = ['growth', 'gain', 'rise', 'increase', 'positive', 'strong', 'boost', 'recovery']
                        negative_words = ['decline', 'fall', 'drop', 'crisis', 'recession', 'negative', 'weak', 'crash']

                        positive_count = sum(1 for word in positive_words if word in content)
                        negative_count = sum(1 for word in negative_words if word in content)

                        if positive_count > negative_count:
                            overall_sentiment = 0.1
                        elif negative_count > positive_count:
                            overall_sentiment = -0.1
                        else:
                            overall_sentiment = 0.0

                    total_sentiment_score += overall_sentiment
                    article_count += 1

                    # Categorize sentiment
                    if overall_sentiment > 0.1:
                        positive_articles += 1
                    elif overall_sentiment < -0.1:
                        negative_articles += 1
                    else:
                        neutral_articles += 1

                    # Check for market-affecting keywords
                    for keyword in market_keywords:
                        if keyword in content:
                            if keyword not in keyword_mentions:
                                keyword_mentions[keyword] = {
                                    'count': 0,
                                    'sentiment_sum': 0.0
                                }
                            keyword_mentions[keyword]['count'] += 1
                            keyword_mentions[keyword]['sentiment_sum'] += overall_sentiment

                except Exception as e:
                    print(f"⚠️ Error processing article: {e}")
                    continue

            # Define high-impact keywords that should have increased weighting
            high_impact_keywords = {
                'inflation': 3.0,      # 3x weight - inflation is critical for market sentiment
                'fed': 2.5,            # 2.5x weight - Federal Reserve decisions are major market movers
                'federal reserve': 2.5, # 2.5x weight - Same as 'fed'
                'interest rates': 2.5   # 2.5x weight - Interest rate changes significantly impact markets
            }

            # Calculate overall market sentiment
            if article_count > 0:
                base_sentiment = total_sentiment_score / article_count
            else:
                base_sentiment = 0.0

            print(f"📊 Base sentiment from articles: {base_sentiment:.6f}")

            # Calculate weighted keyword influence on overall sentiment
            total_keyword_impact = 0.0
            total_keyword_weight = 0.0

            for keyword, data in keyword_mentions.items():
                if data['count'] > 0:
                    weight_multiplier = high_impact_keywords.get(keyword, 1.0)
                    keyword_weight = min(data['count'], 10) * weight_multiplier
                    keyword_impact = (data['sentiment_sum'] / data['count']) * keyword_weight

                    total_keyword_impact += keyword_impact
                    total_keyword_weight += keyword_weight

            # Blend base sentiment with weighted keyword sentiment
            if total_keyword_weight > 0:
                keyword_weighted_sentiment = total_keyword_impact / total_keyword_weight
                # Keyword influence can be up to 40% of final sentiment for high-impact keywords
                keyword_influence = min(total_keyword_weight / 25.0, 0.4)
                average_sentiment = (base_sentiment * (1 - keyword_influence)) + (keyword_weighted_sentiment * keyword_influence)

                print(f"📊 Keyword-weighted sentiment: {keyword_weighted_sentiment:.6f}")
                print(f"📊 Keyword influence: {keyword_influence:.3f} ({keyword_influence*100:.1f}%)")
                print(f"📊 Final blended sentiment: {average_sentiment:.6f}")
            else:
                average_sentiment = base_sentiment
                print(f"📊 No keyword influence, using base sentiment: {average_sentiment:.6f}")

            # Calculate keyword sentiment scores with enhanced weighting for critical keywords
            keyword_sentiment_scores = {}

            for keyword, data in keyword_mentions.items():
                if data['count'] > 0:
                    avg_sentiment = data['sentiment_sum'] / data['count']

                    # Apply enhanced weighting for high-impact keywords
                    weight_multiplier = high_impact_keywords.get(keyword, 1.0)
                    base_impact = avg_sentiment * min(data['count'], 10)
                    weighted_impact = base_impact * weight_multiplier

                    keyword_sentiment_scores[keyword] = {
                        'mentions': data['count'],
                        'average_sentiment': avg_sentiment,
                        'impact_score': weighted_impact,
                        'weight_multiplier': weight_multiplier,
                        'base_impact': base_impact
                    }

                    # Log high-impact keyword processing
                    if weight_multiplier > 1.0:
                        print(f"🔥 High-impact keyword '{keyword}': {data['count']} mentions, "
                              f"sentiment {avg_sentiment:.3f}, base impact {base_impact:.3f}, "
                              f"weighted impact {weighted_impact:.3f} (x{weight_multiplier})")


            # Determine market sentiment level using updated responsive thresholds
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            sentiment_level = MarketSentimentSerializer._get_sentiment_level(average_sentiment)

            # Create market sentiment record
            current_time = unixTimeNow()

            market_sentiment = MarketSentiment(
                sentiment_score=average_sentiment,
                sentiment_level=sentiment_level,
                total_articles=article_count,
                positive_articles=positive_articles,
                negative_articles=negative_articles,
                neutral_articles=neutral_articles,
                keyword_mentions=json.dumps(keyword_sentiment_scores),
                top_keywords=json.dumps(list(sorted(keyword_mentions.keys(),
                                                  key=lambda k: keyword_mentions[k]['count'],
                                                  reverse=True)[:10])),
                analysis_timestamp=current_time,
                status_id=1
            )

            db.add(market_sentiment)
            db.commit()

            print(f"✅ Market sentiment analysis completed and saved to database")

            return {
                "success": True,
                "message": "Market sentiment analysis completed successfully",
                "sentiment_score": average_sentiment,
                "sentiment_level": sentiment_level,
                "articles_processed": article_count,
                "positive_articles": positive_articles,
                "negative_articles": negative_articles,
                "neutral_articles": neutral_articles,
                "keyword_mentions": len(keyword_mentions),
                "top_keywords": list(sorted(keyword_mentions.keys(),
                                          key=lambda k: keyword_mentions[k]['count'],
                                          reverse=True)[:5]),
                "source_stats": source_stats,
                "total_sources": len([s for s in source_stats.values() if s > 0])
            }

        except Exception as e:
            print(f"❌ Error in manual market sentiment analysis: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}

            return {"success": True, "message": "Market sentiment analysis test completed", "analysis_result": analysis_result}
    except Exception as e:
        print(f"❌ TestMarketSentimentTrigger endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None



@apiDecorator.protectedRoute('/test/sentiment_trading_controls', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testSentimentTradingControls():
    """Test endpoint to verify sentiment-based trading controls"""
    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

    try:
        # Test buy order sentiment check
        buy_check = MarketSentimentSerializer.should_allow_trading("buy")

        # Test sell order sentiment check
        sell_check = MarketSentimentSerializer.should_allow_trading("sell")

        # Get current sentiment
        current_sentiment = MarketSentimentSerializer.get_current_sentiment()

        return {
            "success": True,
            "message": "Sentiment-based trading controls test completed",
            "current_sentiment": current_sentiment.get("sentiment", {}) if current_sentiment.get("success") else None,
            "buy_order_check": {
                "allowed": buy_check["allow_trading"],
                "reason": buy_check["reason"],
                "recommendation": buy_check["recommendation"]
            },
            "sell_order_check": {
                "allowed": sell_check["allow_trading"],
                "reason": sell_check["reason"],
                "recommendation": sell_check["recommendation"]
            },
            "trading_rules": {
                "buy_orders": "Blocked when sentiment is Negative or Very Negative",
                "sell_orders": "Always allowed, encouraged when sentiment is negative",
                "emergency_sells": "Triggered automatically when sentiment becomes Very Negative"
            }
        }

    except Exception as e:
        print(f"❌ Error testing sentiment trading controls: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

@apiDecorator.protectedRoute('/test/sentiment_calculation', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testSentimentCalculation():
    """Test endpoint to verify the new sentiment calculation logic"""
    try:
        # Simulate the data from your example
        test_data = {
            "total_articles": 102,
            "positive_articles": 69,
            "negative_articles": 30,
            "neutral_articles": 3,
            "base_sentiment": 0.067200,  # Original calculation
            "keyword_data": {
                "war": {"mentions": 19, "avg_sentiment": -0.462817827067669},
                "inflation": {"mentions": 15, "avg_sentiment": -0.6431746031746033},
                "fed": {"mentions": 13, "avg_sentiment": -0.40512820512820513},
                "tariffs": {"mentions": 11, "avg_sentiment": -0.15584415584415587},
                "federal reserve": {"mentions": 10, "avg_sentiment": -0.39333333333333337},
                "trump": {"mentions": 29, "avg_sentiment": -0.22922824302134648},
                "interest rates": {"mentions": 18, "avg_sentiment": -0.12962962962962962},
                "trade war": {"mentions": 6, "avg_sentiment": -0.3533430555555556},
                "forecast": {"mentions": 4, "avg_sentiment": -0.5119047619047619},
                "ppi": {"mentions": 2, "avg_sentiment": -0.1608105}
            }
        }

        # Calculate using the new logic
        total_keyword_impact = 0.0
        total_keyword_weight = 0.0
        keyword_scores = {}

        for keyword, data in test_data["keyword_data"].items():
            weight = min(data["mentions"], 10)
            impact_score = data["avg_sentiment"] * weight

            keyword_scores[keyword] = {
                "mentions": data["mentions"],
                "avg_sentiment": data["avg_sentiment"],
                "impact_score": impact_score
            }

            total_keyword_impact += impact_score
            total_keyword_weight += weight

        base_sentiment = test_data["base_sentiment"]

        if total_keyword_weight > 0:
            keyword_weighted_sentiment = total_keyword_impact / total_keyword_weight
            keyword_influence = min(total_keyword_weight / 20.0, 0.3)
            final_sentiment = (base_sentiment * (1 - keyword_influence)) + (keyword_weighted_sentiment * keyword_influence)
        else:
            final_sentiment = base_sentiment
            keyword_weighted_sentiment = 0
            keyword_influence = 0

        # Determine sentiment level using updated responsive thresholds
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
        sentiment_level = MarketSentimentSerializer._get_sentiment_level(final_sentiment)

        return {
            "success": True,
            "message": "Sentiment calculation test completed",
            "original_data": test_data,
            "calculation_results": {
                "base_sentiment": base_sentiment,
                "keyword_weighted_sentiment": keyword_weighted_sentiment,
                "keyword_influence": keyword_influence,
                "final_sentiment": final_sentiment,
                "sentiment_level": sentiment_level,
                "total_keyword_weight": total_keyword_weight,
                "total_keyword_impact": total_keyword_impact
            },
            "top_negative_keywords": sorted(
                [(k, v) for k, v in keyword_scores.items() if v["impact_score"] < -1.0],
                key=lambda x: x[1]["impact_score"]
            )[:5]
        }

    except Exception as e:
        print(f"❌ Error testing sentiment calculation: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


#################################################
########  Symbol Sentiment Analysis (Every 5 minutes)
#################################################
@app.schedule(Cron('*/15', '*', '?', '*', '*', '*'))
def symbolSentimentAnalysis(event):
    """
    Symbol sentiment analysis cron job that runs every 15 minutes.
    Monitors sentiment for each symbol in current open positions using AlphaAdvantage news API.
    Triggers individual symbol sells when sentiment drops significantly.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Utils.database import get_db_session_with_retry
    from chalicelib.Utils.functions import unixTimeNow
    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

    print("🔍 Starting symbol sentiment analysis for open positions...")

    try:
        # Get all symbols from current open positions (no database connection needed here)
        open_symbols = MarketSentimentSerializer.get_current_open_position_symbols()

        if not open_symbols:
            print("📊 No open positions found - skipping symbol sentiment analysis")
            return {"success": True, "message": "No open positions to analyze"}

        print(f"📊 Analyzing sentiment for {len(open_symbols)} symbols: {open_symbols}")

        analysis_results = []
        sell_triggers = []

        # Analyze sentiment for each symbol (API calls don't need database connections)
        for symbol in open_symbols:
            try:
                # Analyze symbol sentiment (using 3-hour window) - this is an API call
                sentiment_result = MarketSentimentSerializer.analyze_symbol_sentiment(symbol, time_from_hours=3)

                if sentiment_result.get("success"):
                    # Store sentiment data in database - uses its own connection management
                    storage_result = MarketSentimentSerializer.store_symbol_sentiment(sentiment_result)

                    if not storage_result.get("success"):
                        # Log storage failure for debugging
                        pass

                    # Check if we should trigger sells for this symbol - uses its own connection management
                    sell_result = MarketSentimentSerializer.trigger_symbol_sentiment_sells(symbol, sentiment_result)

                    if sell_result.get("sells_triggered", 0) > 0:
                        sell_triggers.append({
                            "symbol": symbol,
                            "sells_triggered": sell_result["sells_triggered"],
                            "reason": sell_result.get("trigger_reason", "Unknown"),
                            "sentiment_level": sell_result.get("sentiment_level", "Unknown")
                        })
                        print(f"🚨 Triggered {sell_result['sells_triggered']} emergency sells for {symbol}")

                    analysis_results.append({
                        "symbol": symbol,
                        "sentiment_level": sentiment_result["sentiment_level"],
                        "sentiment_score": sentiment_result["sentiment_score"],
                        "total_articles": sentiment_result["total_articles"],
                        "success": True
                    })

                else:
                    print(f"❌ Failed to analyze sentiment for {symbol}: {sentiment_result.get('error')}")
                    analysis_results.append({
                        "symbol": symbol,
                        "success": False,
                        "error": sentiment_result.get("error")
                    })

            except Exception as e:
                print(f"❌ Error processing symbol {symbol}: {e}")
                analysis_results.append({
                    "symbol": symbol,
                    "success": False,
                    "error": str(e)
                })
                continue

        # Summary
        successful_analyses = [r for r in analysis_results if r.get("success")]
        failed_analyses = [r for r in analysis_results if not r.get("success")]
        total_sells_triggered = sum(st["sells_triggered"] for st in sell_triggers)

        print(f"📊 Symbol sentiment analysis complete:")
        print(f"   ✅ Successful: {len(successful_analyses)}/{len(open_symbols)} symbols")
        print(f"   ❌ Failed: {len(failed_analyses)} symbols")
        print(f"   🚨 Emergency sells triggered: {total_sells_triggered} across {len(sell_triggers)} symbols")

        if sell_triggers:
            for trigger in sell_triggers:
                print(f"      🔴 {trigger['symbol']}: {trigger['sells_triggered']} sells ({trigger['sentiment_level']}) - {trigger['reason']}")

        return {
            "success": True,
            "message": f"Symbol sentiment analysis completed for {len(open_symbols)} symbols",
            "symbols_analyzed": len(successful_analyses),
            "symbols_failed": len(failed_analyses),
            "emergency_sells_triggered": total_sells_triggered,
            "sell_triggers": sell_triggers,
            "analysis_results": analysis_results
        }

    except Exception as e:
        print(f"❌ Error in symbol sentiment analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


@apiDecorator.protectedRoute('/test/symbol_sentiment_cron', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testSymbolSentimentCron():
    """Test endpoint to manually trigger the symbol sentiment cron job (for testing only)"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            print("🧪 Testing symbol sentiment cron job...")

            # Call the actual cron job function logic directly
            from chalicelib.Utils.functions import unixTimeNow
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

            print("🔍 Starting symbol sentiment analysis for open positions...")

            # Get all symbols from current open positions (limit to 5 for testing)
            open_symbols = MarketSentimentSerializer.get_current_open_position_symbols()[:5]

            if not open_symbols:
                result = {"success": True, "message": "No open positions to analyze"}
            else:
                print(f"📊 Analyzing sentiment for {len(open_symbols)} symbols: {open_symbols}")

                analysis_results = []
                sell_triggers = []

                # Analyze sentiment for each symbol
                for symbol in open_symbols:
                    try:
                        print(f"🔍 Analyzing sentiment for {symbol}...")

                        # Analyze symbol sentiment (using 3-hour window)
                        sentiment_result = MarketSentimentSerializer.analyze_symbol_sentiment(symbol, time_from_hours=3)

                        if sentiment_result.get("success"):
                            # Store sentiment data in database
                            storage_result = MarketSentimentSerializer.store_symbol_sentiment(sentiment_result)

                            if storage_result.get("success"):
                                print(f"✅ Stored sentiment for {symbol}: {sentiment_result['sentiment_level']}")
                            else:
                                print(f"⚠️ Failed to store sentiment for {symbol}: {storage_result.get('error')}")

                            # Check if we should trigger sells for this symbol
                            sell_result = MarketSentimentSerializer.trigger_symbol_sentiment_sells(symbol, sentiment_result)

                            if sell_result.get("sells_triggered", 0) > 0:
                                sell_triggers.append({
                                    "symbol": symbol,
                                    "sells_triggered": sell_result["sells_triggered"],
                                    "reason": sell_result.get("trigger_reason", "Unknown"),
                                    "sentiment_level": sell_result.get("sentiment_level", "Unknown")
                                })
                                print(f"🚨 Triggered {sell_result['sells_triggered']} emergency sells for {symbol}")

                            analysis_results.append({
                                "symbol": symbol,
                                "sentiment_level": sentiment_result["sentiment_level"],
                                "sentiment_score": sentiment_result["sentiment_score"],
                                "total_articles": sentiment_result["total_articles"],
                                "success": True
                            })

                        else:
                            print(f"❌ Failed to analyze sentiment for {symbol}: {sentiment_result.get('error')}")
                            analysis_results.append({
                                "symbol": symbol,
                                "success": False,
                                "error": sentiment_result.get("error")
                            })

                    except Exception as e:
                        print(f"❌ Error processing symbol {symbol}: {e}")
                        analysis_results.append({
                            "symbol": symbol,
                            "success": False,
                            "error": str(e)
                        })
                        continue

                # Summary
                successful_analyses = [r for r in analysis_results if r.get("success")]
                failed_analyses = [r for r in analysis_results if not r.get("success")]
                total_sells_triggered = sum(st["sells_triggered"] for st in sell_triggers)

                print(f"📊 Symbol sentiment analysis complete:")
                print(f"   ✅ Successful: {len(successful_analyses)}/{len(open_symbols)} symbols")
                print(f"   ❌ Failed: {len(failed_analyses)} symbols")
                print(f"   🚨 Emergency sells triggered: {total_sells_triggered} across {len(sell_triggers)} symbols")

                result = {
                    "success": True,
                    "message": f"Symbol sentiment analysis completed for {len(open_symbols)} symbols",
                    "symbols_analyzed": len(successful_analyses),
                    "symbols_failed": len(failed_analyses),
                    "emergency_sells_triggered": total_sells_triggered,
                    "sell_triggers": sell_triggers,
                    "analysis_results": analysis_results
                }

            return {
                "success": True,
                "message": "Symbol sentiment cron job test completed",
                "cron_result": result,
                "warning": "This is a test endpoint - use with caution in production"
            }
    except Exception as e:
        print(f"❌ TestSymbolSentimentCron endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/test/symbol_sentiment', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testSymbolSentiment():
    """Test endpoint to manually trigger symbol sentiment analysis (for testing only)"""
    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

    try:
        data = app.current_request.json_body or {}
        symbol = data.get('symbol')

        if symbol:
            # Test specific symbol
            print(f"🧪 Testing symbol sentiment analysis for: {symbol}")

            # Analyze sentiment (using 3-hour window)
            sentiment_result = MarketSentimentSerializer.analyze_symbol_sentiment(symbol, time_from_hours=3)

            if sentiment_result.get("success"):
                # Store sentiment data
                storage_result = MarketSentimentSerializer.store_symbol_sentiment(sentiment_result)

                # Test sell trigger
                sell_result = MarketSentimentSerializer.trigger_symbol_sentiment_sells(symbol, sentiment_result)

                return {
                    "success": True,
                    "message": f"Symbol sentiment test completed for {symbol}",
                    "sentiment_analysis": sentiment_result,
                    "storage_result": storage_result,
                    "sell_trigger_result": sell_result,
                    "warning": "This is a test endpoint - use with caution in production"
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to analyze sentiment for {symbol}: {sentiment_result.get('error')}"
                }
        else:
            # Test all open positions
            print("🧪 Testing symbol sentiment analysis for all open positions...")

            # Get open symbols
            open_symbols = MarketSentimentSerializer.get_current_open_position_symbols()

            if not open_symbols:
                return {
                    "success": True,
                    "message": "No open positions found to test",
                    "open_symbols": []
                }

            results = []
            for test_symbol in open_symbols[:3]:  # Limit to first 3 for testing
                try:
                    sentiment_result = MarketSentimentSerializer.analyze_symbol_sentiment(test_symbol, time_from_hours=3)
                    if sentiment_result.get("success"):
                        storage_result = MarketSentimentSerializer.store_symbol_sentiment(sentiment_result)
                        sell_result = MarketSentimentSerializer.trigger_symbol_sentiment_sells(test_symbol, sentiment_result)

                        results.append({
                            "symbol": test_symbol,
                            "sentiment_level": sentiment_result["sentiment_level"],
                            "sentiment_score": sentiment_result["sentiment_score"],
                            "total_articles": sentiment_result["total_articles"],
                            "storage_success": storage_result.get("success"),
                            "sells_triggered": sell_result.get("sells_triggered", 0),
                            "success": True
                        })
                    else:
                        results.append({
                            "symbol": test_symbol,
                            "success": False,
                            "error": sentiment_result.get("error")
                        })
                except Exception as e:
                    results.append({
                        "symbol": test_symbol,
                        "success": False,
                        "error": str(e)
                    })

            return {
                "success": True,
                "message": f"Symbol sentiment test completed for {len(results)} symbols",
                "open_symbols": open_symbols,
                "test_results": results,
                "warning": "This is a test endpoint - limited to first 3 symbols"
            }

    except Exception as e:
        print(f"❌ Error testing symbol sentiment: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


@apiDecorator.protectedRoute('/test/emergency_sells', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testEmergencySells():
    """Test endpoint to manually trigger emergency sells (for testing only)"""
    # CRITICAL: This endpoint triggers real sell orders - MUST be disabled in production
    if not is_test_mode_enabled():
        return {"success": False, "error": "Emergency sell test endpoint is disabled in production for safety"}

    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

    try:
        print("🧪 Testing emergency sell trigger...")

        # Get current sentiment first
        current_sentiment = MarketSentimentSerializer.get_current_sentiment()

        # Trigger emergency sells
        result = MarketSentimentSerializer.trigger_sentiment_based_sells()

        return {
            "success": True,
            "message": "Emergency sell test completed",
            "current_sentiment": current_sentiment.get("sentiment", {}) if current_sentiment.get("success") else None,
            "emergency_sell_result": result,
            "warning": "This is a test endpoint - use with caution in production"
        }

    except Exception as e:
        print(f"❌ Error testing emergency sells: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

@apiDecorator.protectedRoute('/test/take_profit_account/{account_uuid}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testTakeProfitForAccount(account_uuid):
    """Test take profit functionality for a specific account"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.models import TradingBot
            from chalicelib.Accounts.models import Account
            from chalicelib.Portfolios.models import AccountPortfolio
            from chalicelib.Bots.serializer import AutomationSerializer

            print(f"🧪 Testing take profit for account: {account_uuid}")
        # Get the account
        account = db.query(Account).filter(Account.uuid == account_uuid).first()
        if not account:
            return {
                "success": False,
                "error": f"Account not found: {account_uuid}"
            }

        print(f"✅ Found account: {account.id} - {account.name}")

        # Check if take profit is enabled for this account
        account_portfolio = db.query(AccountPortfolio).filter(
            AccountPortfolio.account_id == account.id
        ).first()

        if not account_portfolio:
            return {
                "success": False,
                "error": f"No account portfolio found for account: {account_uuid}"
            }

        take_profit_enabled = account_portfolio.take_profit == 1
        daily_profit_target = account_portfolio.daily_profit_target

        print(f"✅ Take profit enabled: {take_profit_enabled}")
        print(f"✅ Daily profit target: {daily_profit_target}%")

        # Get active bots for this account
        bots = db.query(TradingBot).filter(
            TradingBot.account_id == account.id,
            TradingBot.status_id == 1  # Active bots only
        ).all()

        if not bots:
            return {
                "success": True,
                "message": "No active bots found for this account",
                "account_info": {
                    "account_uuid": account_uuid,
                    "account_name": account.name,
                    "take_profit_enabled": take_profit_enabled,
                    "daily_profit_target": daily_profit_target
                },
                "bots": []
            }

        print(f"✅ Found {len(bots)} active bots")

        # Test take profit for each bot
        bot_results = []
        for bot in bots:
            try:
                print(f"🤖 Testing bot: {bot.uuid}")

                # Get positions for this bot
                positions = AutomationSerializer._get_positions(bot.uuid, app)
                print(f"   📊 Found {len(positions)} positions")

                # Calculate current profit
                total_gain = sum(p.get('gain', 0) for p in positions)

                # Get bot balance
                automation, balance, available_cash = AutomationSerializer._get_balance(bot.uuid, app)

                # Calculate profit percentage
                if float(balance) > 0 and total_gain > 0:
                    profit_pct = (total_gain / float(balance)) * 100
                else:
                    profit_pct = 0

                # Test the actual profit_check function
                profit_check_result = AutomationSerializer.profit_check(bot.uuid, app)

                bot_result = {
                    "bot_uuid": bot.uuid,
                    "bot_type_id": bot.bot_type_id,
                    "brokerage_connected": bot.brokerage_connected,
                    "positions_count": len(positions),
                    "total_gain": total_gain,
                    "balance": float(balance),
                    "available_cash": float(available_cash),
                    "profit_percentage": profit_pct,
                    "meets_profit_target": profit_pct >= daily_profit_target,
                    "profit_check_result": profit_check_result,
                    "positions": [
                        {
                            "symbol": p.get('symbol'),
                            "quantity": p.get('quantity'),
                            "current_price": p.get('current_price'),
                            "gain": p.get('gain'),
                            "percent_change": p.get('percent_change'),
                            "meets_individual_target": p.get('percent_change', 0) >= daily_profit_target
                        } for p in positions
                    ]
                }

                bot_results.append(bot_result)
                print(f"   ✅ Bot test completed - Profit: {profit_pct:.2f}%")

            except Exception as e:
                print(f"   ❌ Error testing bot {bot.uuid}: {str(e)}")
                bot_results.append({
                    "bot_uuid": bot.uuid,
                    "error": str(e)
                })

        return {
            "success": True,
            "message": f"Take profit test completed for account {account_uuid}",
            "account_info": {
                "account_uuid": account_uuid,
                "account_name": account.name,
                "account_id": account.id,
                "take_profit_enabled": take_profit_enabled,
                "daily_profit_target": daily_profit_target
            },
            "bots_tested": len(bots),
            "bots": bot_results,
            "summary": {
                "total_bots": len(bots),
                "bots_with_positions": len([b for b in bot_results if b.get('positions_count', 0) > 0]),
                "bots_meeting_target": len([b for b in bot_results if b.get('meets_profit_target', False)]),
                "total_positions": sum(b.get('positions_count', 0) for b in bot_results),
                "total_gain": sum(b.get('total_gain', 0) for b in bot_results)
            }
            }
    except Exception as e:
        print(f"❌ TestTakeProfitForAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/update_positions', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testUpdatePositions():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # CRITICAL: This endpoint updates live position data - MUST be disabled in production
            if not is_test_mode_enabled():
                return {"success": False, "error": "Position update test endpoint is disabled in production for safety"}

            from chalicelib.Bots.models import TradingBot
            automations = db.query(TradingBot).filter(TradingBot.status_id == 1).all()
            for a in automations:
                ## Update account balances
                if os.environ['STAGE'] != 'local':
                    if a.brokerage_connected:
                        body = {
                            "automation_uuid": a.uuid
                        }
                        payload = {
                            "multiValueQueryStringParameters": None,
                            "headers": {
                                "x-api-key": os.environ['AI_API_KEY'],
                                "access-token": None,
                                "refresh-token": None,
                                "account-id": None
                            },
                            "pathParameters": None,
                            "requestContext": {
                                "httpMethod": None,
                                "resourcePath": None
                            },
                            "body": None,
                            "stageVariables": {
                                "stage": os.environ['STAGE']
                            }
                        }
                        payload['body'] = json.dumps(body)

                        ## Call the lambda
                        lambdaName = os.environ['POSITION_UPDATE_LAMBDA']
                        lambda_response = boto3.client('lambda').invoke(
                            FunctionName=lambdaName,
                            InvocationType='Event',
                            Payload=json.dumps(payload)
                        )
                else:
                    if a.brokerage_connected:
                        AutomationSerializer.update_positions(a.uuid, app)
            return {"success": True}
    except Exception as e:
        print(f"❌ TestUpdatePositions endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/test/update_cooldowns', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testCooldownUpdate():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Bots.models import TradingBot
            from chalicelib.Portfolios.models import Portfolio

            symbols = db.query(Portfolio).filter(Portfolio.cooldown == True).all()
            for s in symbols:
                s.cooldown = False
            try:
                db.commit()
            except Exception as e:
                db.rollback()
                print(e)
                print(traceback.format_exc())
            return {"success": True}
    except Exception as e:
        print(f"❌ TestCooldownUpdate endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None




@apiDecorator.protectedRoute('/trade/cancel_all_orders_all_accounts', methods=['GET'], cors=corsConfig)
def cancelAllOrdersAllAccounts():
    """
    Cancel all open orders for ALL active trading bots across all accounts.
    This processes synchronously (not asynchronously) and affects all accounts.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.serializer import AutomationSerializer
    from chalicelib.Bots.models import TradingBot, Trades
    from chalicelib.Accounts.models import Account
    from chalicelib.Utils.functions import ChaliceRequest
    import traceback
    import time

    try:
        with quick_session() as db:
            app.current_request.db = db

            start_time = time.time()
            print(f"🚫 Starting cancel ALL orders for ALL accounts")

            # Get all active trading bots
            active_bots = db.query(TradingBot).filter(
                TradingBot.status_id == 1,  # Active bots only
                TradingBot.brokerage_connected == True  # Connected bots only
            ).all()

            print(f"🤖 Found {len(active_bots)} active trading bots")

            # Get all pending orders across all bots
            all_pending_orders = db.query(Trades).join(
                TradingBot, Trades.trading_bot_id == TradingBot.id
            ).filter(
                Trades.status_id == 4,  # Status 4 = pending
                TradingBot.status_id == 1,  # Active bots only
                TradingBot.brokerage_connected == True
            ).all()

            # Create action type lookup
            from chalicelib.Portfolios.models import ActionTypes
            action_types = db.query(ActionTypes).all()
            action_type_lookup = {at.id: at.name for at in action_types}

            print(f"📋 Found {len(all_pending_orders)} total pending orders across all accounts")

            # Group orders by account for reporting
            orders_by_account = {}
            cancelled_orders = []
            failed_cancellations = []

            for order in all_pending_orders:
                account_id = order.automation.account_id
                if account_id not in orders_by_account:
                    orders_by_account[account_id] = {
                        'bot_uuid': order.automation.uuid,
                        'pending_orders': [],
                        'cancelled': [],
                        'failed': []
                    }
                orders_by_account[account_id]['pending_orders'].append(order)

            print(f"🏢 Processing orders across {len(orders_by_account)} accounts")

            # Process each order
            for order in all_pending_orders:
                try:
                    account_id = order.automation.account_id
                    action_name = action_type_lookup.get(order.action_type_id, 'Unknown')
                    print(f"🚫 Cancelling order: {order.uuid} - {order.symbol} {action_name} (Account: {account_id})")

                    # Cancel the order using the existing cancel_order method
                    result = AutomationSerializer.cancel_order(order.uuid, app)

                    order_info = {
                        'trade_uuid': order.uuid,
                        'symbol': order.symbol,
                        'action': action_name,
                        'quantity': float(order.quantity) if order.quantity else 0,
                        'account_id': account_id,
                        'bot_uuid': order.automation.uuid
                    }

                    if result.get('success', False):
                        # Extract the updated trade info from the result
                        trade_data = result.get('trade', {})
                        order_info['status'] = 'cancelled'
                        order_info['broker_order_number'] = trade_data.get('broker_order_number') if trade_data else None
                        order_info['cancelled_at'] = trade_data.get('datestamp') if trade_data else None
                        cancelled_orders.append(order_info)
                        orders_by_account[account_id]['cancelled'].append(order_info)
                        print(f"✅ Successfully cancelled order {order.uuid}")
                    else:
                        order_info['error'] = result.get('message', 'Unknown error')
                        failed_cancellations.append(order_info)
                        orders_by_account[account_id]['failed'].append(order_info)
                        print(f"❌ Failed to cancel order {order.uuid}: {result.get('message', 'Unknown error')}")

                except Exception as e:
                    error_msg = str(e)
                    action_name = action_type_lookup.get(order.action_type_id, 'Unknown')
                    order_info = {
                        'trade_uuid': order.uuid,
                        'symbol': order.symbol,
                        'action': action_name,
                        'quantity': float(order.quantity) if order.quantity else 0,
                        'account_id': order.automation.account_id,
                        'bot_uuid': order.automation.uuid,
                        'error': error_msg
                    }
                    failed_cancellations.append(order_info)
                    orders_by_account[order.automation.account_id]['failed'].append(order_info)
                    print(f"❌ Exception cancelling order {order.uuid}: {error_msg}")

            # Calculate summary statistics
            total_orders = len(all_pending_orders)
            successful_cancellations = len(cancelled_orders)
            failed_count = len(failed_cancellations)
            execution_time = time.time() - start_time

            # Create account summaries
            account_summaries = []
            for account_id, data in orders_by_account.items():
                account_summaries.append({
                    'account_id': account_id,
                    'bot_uuid': data['bot_uuid'],
                    'total_orders': len(data['pending_orders']),
                    'cancelled': len(data['cancelled']),
                    'failed': len(data['failed'])
                })

            print(f"📊 Global Cancellation Summary: {successful_cancellations}/{total_orders} successful in {execution_time:.2f}s")

            return {
                'success': True,
                'execution_time_seconds': round(execution_time, 2),
                'global_summary': {
                    'total_pending_orders': total_orders,
                    'successful_cancellations': successful_cancellations,
                    'failed_cancellations': failed_count,
                    'accounts_processed': len(orders_by_account),
                    'bots_processed': len(active_bots)
                },
                'account_summaries': account_summaries,
                'cancelled_orders': cancelled_orders,
                'failed_cancellations': failed_cancellations,
                'message': f'Processed {total_orders} orders across {len(orders_by_account)} accounts: {successful_cancellations} cancelled, {failed_count} failed'
            }

    except Exception as e:
        print(f"❌ CancelAllOrdersAllAccounts endpoint error: {e}")
        print(traceback.format_exc())
        emergency_cleanup()
        return {
            'success': False,
            'message': f'Error cancelling all orders: {str(e)}'
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/test/stop-loss-monitor', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testStopLossMonitor():
    """
    Test endpoint to trigger the new stop loss monitoring system.
    This endpoint manually triggers the stop loss monitoring that runs every 30 minutes.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.serializer import AutomationSerializer
    from chalicelib.Utils.functions import ChaliceRequest

    try:
        with quick_session() as db:
            # Create mock request for the automation serializer
            mock_event = {
                "headers": {"x-api-key": "test_stop_loss_monitor"},
                "body": None
            }
            mock_request = ChaliceRequest(mock_event)
            mock_request.db = db

            # Create mock app object
            class MockApp:
                def __init__(self):
                    self.current_request = mock_request

            mock_app = MockApp()

            # Run the stop loss monitoring
            result = AutomationSerializer.monitor_stop_losses(mock_app)

            return {
                "success": True,
                "message": "Stop loss monitoring test completed",
                "results": result
            }

    except Exception as e:
        print(f"❌ TestStopLossMonitor endpoint error: {e}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            "success": False,
            "error": str(e),
            "message": "Stop loss monitoring test failed"
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/stop_loss', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testStopLossOrders():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            # CRITICAL: This endpoint triggers stop loss orders - MUST be disabled in production
            # if not is_test_mode_enabled():
            #     return {"success": False, "error": "Stop loss test endpoint is disabled in production for safety"}

            from chalicelib.Portfolios.serializer import PortfolioSerializer
            from chalicelib.Bots.models import TradingBot
            from chalicelib.Utils.functions import ChaliceRequest
            from chalicelib.Portfolios.models import Portfolio
            from chalicelib.Accounts.models import Account
            from termcolor import colored
            from chalicelib.lambda_resolver import get_lambda_arn

            ## Get symbols

            symbols = PortfolioSerializer._get_all_symbols_in_portfolios(app, STOP_LOSS=True)

            for s in symbols:
                body = {
                    "symbol": s,
                    "market_session": "stop_loss"
                }
                portfolios = db.query(Portfolio).filter(Portfolio.symbol == s, Portfolio.active == True).all()
                for p in portfolios:
                    bots = db.query(TradingBot).join(Account, Account.id == TradingBot.account_id).filter(TradingBot.status_id == 1, TradingBot.brokerage_connected == 1, Account.auto_stop_loss == 1).all()
                    for bot in bots:
                        body = {
                            "symbol": s,
                            "bot_uuid": bot.uuid,
                            "market_session": "stop_loss"
                        }
                        payload = {
                            "multiValueQueryStringParameters": None,
                            "headers": {
                                "x-api-key": os.environ['AI_API_KEY'],
                                "access-token": None,
                                "refresh-token": None,
                                "account-id": None
                            },
                            "pathParameters": None,
                            "requestContext": {
                                "httpMethod": None,
                                "resourcePath": None
                            },
                            "body": None,
                            "stageVariables": {
                                "stage": os.environ['STAGE']
                            }
                        }
                        payload['body'] = json.dumps(body)

                        if os.environ['STAGE'] != 'local':
                            ## Call the lambda
                            lambdaName = get_lambda_arn(os.environ['ANALYZE_SYMBOL_LAMBDA'])
                            lambda_response = boto3.client('lambda').invoke(
                                FunctionName=lambdaName,
                                InvocationType='Event',
                                Payload=json.dumps(payload)
                            )

                        else:
                            print(colored(json.dumps(payload, indent=2),"blue"))
                            AutomationSerializer.stop_loss(body,app)
            return {"success": True}
    except Exception as e:
        print(f"❌ TestStopLossOrders endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/test/sagemaker/create', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testSagemakerCreateEndpoint():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SageMakerSerializer.create_endpoint()
            return response
    except Exception as e:
        print(f"❌ TestSagemakerCreateEndpoint endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/sagemaker/delete', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testSagemakerDeleteEndpoint():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SageMakerSerializer.delete_endpoint()
            return response
    except Exception as e:
        print(f"❌ TestSagemakerDeleteEndpoint endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/test/sagemaker/describe', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testSagemakerCreateEndpoint():
    response = SageMakerSerializer.describe_endpoint()
    return response

@apiDecorator.protectedRoute('/test/symbols/prune', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testPruneSymbolsLambda():
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            import requests
            from chalicelib.Bots.models import TradingBot, Trades
            from chalicelib.Portfolios.models import AccountPortfolio, Portfolio

            ## Create the payload
            payload = {
                "multiValueQueryStringParameters": None,
                "headers": {
                    "x-api-key": os.environ['AI_API_KEY'],
                    "access-token": None,
                    "refresh-token": None,
                    "account-id": None
                },
                "pathParameters": None,
                "requestContext": {
                    "httpMethod": None,
                    "resourcePath": None
                },
                "body": None,
                "stageVariables": {
                    "stage": os.environ['STAGE']
                }
            }
            acctPreferences = db.query(AccountPortfolio).filter(AccountPortfolio.prune_losers == 1).all()
            accts = [x.account_id for x in acctPreferences]
            symbol_query = db.query(Portfolio).filter(Portfolio.account_id.in_(accts)).all()
            symbol_list = [x.symbol for x in symbol_query]
            symbols = list(set(symbol_list))

            for s in symbols:
                body = {
                    "symbol": s
                }
                if os.environ['STAGE'] != 'local':
                    payload['body'] = json.dumps(body)
                    ## Call the lambda
                    lambdaName = os.environ['PRUNE_CHECK_LAMBDA']
                    lambda_response = boto3.client('lambda').invoke(
                        FunctionName=lambdaName,
                        InvocationType='RequestResponse',
                        Payload=json.dumps(payload)
                    )
                    print(lambda_response)
                    res = json.loads(lambda_response['Payload'].read())
                    print(res)
                else:
                    url = f"{os.environ['AI_URL']}/symbol/prune"
                    headers = {
                        "x-api-key": app.current_request.headers['x-api-key']
                    }
                    res = requests.post(url=url, headers=headers, json=body)
                    res = res.json()

                if res.get('status_id'):
                    symbol_query = db.query(Portfolio).filter(Portfolio.account_id.in_(accts), Portfolio.symbol == s).all()
                    for sq in symbol_query:
                        sq.status_id = res['status_id']

                    try:
                        db.commit()
                    except:
                        print(traceback.format_exc())
                        db.flush()
                        db.rollback()
            return payload
    except Exception as e:
        print(f"❌ TestPruneSymbolsLambda endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################
########  Day Trader Sentiment Analysis (Every 5 minutes)
#################################################
@app.schedule(Cron('*/15', '*', '?', '*', '*', '*'))
def daytraderSentimentAnalysis(event):
    """
    Day trader sentiment analysis cron job that runs every 15 minutes.
    Monitors sentiment for symbols in active day trader portfolios with balance > $25,000.

    IMPORTANT:
    - Analysis is suspended when market sentiment is below neutral (Negative/Very Negative)
    - ALL BUY ORDERS SYSTEM-WIDE are blocked when market sentiment is below neutral

    Generates buy signals based on sentiment changes:
    - Neutral → Positive: Trigger symbol analysis (if market sentiment allows)
    - Neutral → Very Positive OR Negative → Very Positive OR 2+ level jump: Direct buy (if market sentiment allows)

    Note: Even if signals are generated, the actual buy orders will be blocked at the system level
    if market sentiment drops below neutral between signal generation and order execution.
    """
    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

    print("🔍 Starting day trader sentiment analysis...")

    try:
        # Run the day trader sentiment analysis
        result = MarketSentimentSerializer.analyze_daytrader_sentiment_signals()

        if result.get("success"):
            print(f"✅ Day trader sentiment analysis completed: {result['message']}")
            print(f"📊 Symbols analyzed: {result.get('symbols_analyzed', 0)}")
            print(f"🚨 Signals generated: {result.get('signals_generated', 0)}")
        else:
            print(f"❌ Day trader sentiment analysis failed: {result.get('error')}")

        return result

    except Exception as e:
        print(f"❌ Error in day trader sentiment analysis cron: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e)
        }


#################################################
########  Stop Loss Position Monitoring (Every 30 minutes)
#################################################
@app.schedule(Cron('*/30', '04-23', '?', '*', '2-6', '*'))
def stopLossPositionMonitoring(event):
    """
    Stop loss position monitoring cron job that runs every 30 minutes during trading hours.
    Monitors all active positions and triggers immediate sell orders when positions exceed their stop loss thresholds.
    Runs during all trading hours (premarket 04:00-13:30, regular 13:30-20:00, extended 20:00-24:00 UTC).
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Bots.serializer import AutomationSerializer
    from chalicelib.Utils.functions import ChaliceRequest

    print("🛑 Starting stop loss position monitoring...")

    try:
        with quick_session() as db:
            # Create mock request for the automation serializer
            mock_event = {
                "headers": {"x-api-key": "stop_loss_monitor"},
                "body": None
            }
            mock_request = ChaliceRequest(mock_event)
            mock_request.db = db

            # Create mock app object
            class MockApp:
                def __init__(self):
                    self.current_request = mock_request

            mock_app = MockApp()

            # Run the stop loss monitoring
            result = AutomationSerializer.monitor_stop_losses(mock_app)

            print(f"🏁 Stop loss monitoring completed:")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Positions checked: {result.get('positions_checked', 0)}")
            print(f"   Stop losses triggered: {result.get('stop_losses_triggered', 0)}")
            print(f"   Errors: {len(result.get('errors', []))}")

            if result.get('errors'):
                for error in result['errors']:
                    print(f"   ❌ {error}")

            return result

    except Exception as e:
        print(f"❌ Error in stop loss position monitoring: {e}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'positions_checked': 0,
            'stop_losses_triggered': 0
        }


#################################################
########  Day Trader Position Monitoring (Every 30 minutes)
#################################################
@app.schedule(Cron('*/30', '*', '?', '*', '*', '*'))
def daytraderPositionMonitoring(event):
    """
    Day trader position monitoring cron job that runs every 30 minutes.
    Monitors existing day trader positions and analyzes for sell signals.
    Triggers sell orders when negative sentiment is detected.
    OPTIMIZED for proper database connection management.
    """
    from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

    print("🔍 Starting day trader position monitoring...")

    try:
        # Run the day trader position monitoring
        result = MarketSentimentSerializer.monitor_daytrader_positions()

        if result.get("success"):
            print(f"✅ Day trader position monitoring completed: {result['message']}")
            print(f"📊 Positions analyzed: {result.get('positions_analyzed', 0)}")
            print(f"🔴 Sell signals: {result.get('sell_signals', 0)}")
        else:
            print(f"❌ Day trader position monitoring failed: {result.get('error')}")

        return result

    except Exception as e:
        print(f"❌ Error in day trader position monitoring cron: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "error": str(e)
        }


#################################################
########  Test Endpoints for Day Trader Sentiment
#################################################
@apiDecorator.protectedRoute('/test/daytrader_sentiment_analysis', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testDaytraderSentimentAnalysis():
    """Test endpoint to manually trigger the day trader sentiment analysis (for testing only)
    This will check sentiment changes, call AI analysis service, evaluate scores (threshold: 28), and execute buy orders if criteria are met."""
    try:
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

        print("🧪 Testing day trader sentiment analysis...")

        # Call the actual cron job function logic directly
        result = MarketSentimentSerializer.analyze_daytrader_sentiment_signals()

        return {
            'success': True,
            'test_completed': True,
            'cron_result': result,
            'message': 'Day trader sentiment analysis test completed'
        }

    except Exception as e:
        print(f"❌ Error in test day trader sentiment analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Day trader sentiment analysis test failed'
        }


@apiDecorator.protectedRoute('/test/daytrader_position_monitoring', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testDaytraderPositionMonitoring():
    """Test endpoint to manually trigger the day trader position monitoring (for testing only)"""
    try:
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

        print("🧪 Testing day trader position monitoring...")

        # Call the actual cron job function logic directly
        result = MarketSentimentSerializer.monitor_daytrader_positions()

        return {
            'success': True,
            'test_completed': True,
            'cron_result': result,
            'message': 'Day trader position monitoring test completed'
        }

    except Exception as e:
        print(f"❌ Error in test day trader position monitoring: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Day trader position monitoring test failed'
        }


@apiDecorator.protectedRoute('/test/daytrader_portfolio_symbols', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testGetDaytraderPortfolioSymbols():
    """Test endpoint to get day trader portfolio symbols (for testing only)"""
    try:
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

        print("🧪 Testing get day trader portfolio symbols...")

        # Get day trader portfolio symbols
        symbols = MarketSentimentSerializer.get_daytrader_portfolio_symbols()

        return {
            'success': True,
            'symbols': symbols,
            'symbol_count': len(symbols),
            'message': 'Day trader portfolio symbols retrieved successfully'
        }

    except Exception as e:
        print(f"❌ Error in test get day trader portfolio symbols: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Get day trader portfolio symbols test failed'
        }


@apiDecorator.protectedRoute('/test/daytrader_available_cash', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testDaytraderAvailableCash():
    """Test endpoint to check available cash for day trader bots (for testing only)"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
            from chalicelib.Bots.models import TradingBot
            from chalicelib.Accounts.models import Account, AccountBalance
            from chalicelib.Portfolios.models import AccountPortfolio
            from sqlalchemy import and_

            print("🧪 Testing day trader available cash...")
            # Get day trader bots with balance > $25,000
            daytrader_bots = db.query(TradingBot).join(
                Account, TradingBot.account_id == Account.id
            ).join(
                AccountBalance, Account.id == AccountBalance.account_id
            ).filter(
                and_(
                    TradingBot.day_trader == True,
                    TradingBot.status_id == 1,  # Active bots
                    AccountBalance.balance > 25000
                )
            ).all()

            results = []
            for bot in daytrader_bots:
                # Get account portfolio settings
                account_portfolio = db.query(AccountPortfolio).filter(
                    AccountPortfolio.account_id == bot.account_id
                ).first()

                # Get current account balance
                balance = db.query(AccountBalance).filter(
                    AccountBalance.account_id == bot.account_id
                ).order_by(AccountBalance.created_at.desc()).first()

                available_cash = 0
                available_cash_ratio = 0
                if account_portfolio and balance:
                    available_cash_ratio = account_portfolio.available_cash_ratio
                    if available_cash_ratio < 100:
                        available_cash = float(balance.balance) * ((100 - available_cash_ratio) / 100)

                has_cash = MarketSentimentSerializer._check_available_cash(bot)

                results.append({
                    'bot_uuid': bot.uuid,
                    'account_id': bot.account_id,
                    'total_balance': float(balance.balance) if balance else 0,
                    'available_cash_ratio': available_cash_ratio,
                    'available_cash': available_cash,
                    'has_available_cash': has_cash
                })

            return {
                'success': True,
                'daytrader_bots': len(daytrader_bots),
                'cash_analysis': results,
                'message': 'Day trader available cash analysis completed'
            }

    except Exception as e:
        print(f"❌ TestDaytraderAvailableCash endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/test/market_sentiment_trading_check', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def testMarketSentimentTradingCheck():
    """Test endpoint to check current market sentiment and trading allowance (for testing only)"""
    try:
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer

        print("🧪 Testing market sentiment trading check...")

        # Check current market sentiment for buy orders
        buy_check = MarketSentimentSerializer.should_allow_trading("buy")

        # Get current sentiment details
        current_sentiment = MarketSentimentSerializer.get_current_sentiment()

        return {
            'success': True,
            'market_sentiment_check': buy_check,
            'current_sentiment': current_sentiment,
            'trading_allowed': buy_check.get('allow_trading', True),
            'sentiment_level': buy_check.get('sentiment_level', 'Unknown'),
            'daytrader_signals_blocked': buy_check.get('sentiment_level') in ['Negative', 'Very Negative'],
            'message': 'Market sentiment trading check completed'
        }

    except Exception as e:
        print(f"❌ Error in test market sentiment trading check: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Market sentiment trading check test failed'
        }


@apiDecorator.protectedRoute('/test/system_wide_buy_block', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def testSystemWideBuyBlock():
    """Test endpoint to verify system-wide buy order blocking when market sentiment is below neutral (for testing only)"""
    try:
        from chalicelib.Bots.serializer import AutomationSerializer
        from chalicelib.MarketSentiment.serializer import MarketSentimentSerializer
        from chalicelib.Utils.functions import ChaliceRequest

        print("🧪 Testing system-wide buy order blocking...")

        # Check current market sentiment
        sentiment_check = MarketSentimentSerializer.should_allow_trading("buy")
        sentiment_level = sentiment_check.get("sentiment_level", "Unknown")

        # Create a mock buy order request
        mock_buy_data = {
            'symbol': 'AAPL',
            'quantity': 1,
            'account_id': 1,  # Test account
            'bot_uuid': 'test-bot-uuid',
            'action': 'buy',
            'action_type_id': 1,
            'reason': 'System-wide buy block test'
        }

        # Create mock request
        mock_request = ChaliceRequest()

        try:
            # Attempt to place a buy order
            result = AutomationSerializer.buy(mock_buy_data, mock_request)

            # If we get here, the buy order was allowed
            return {
                'success': True,
                'buy_order_blocked': False,
                'buy_order_result': result,
                'market_sentiment_level': sentiment_level,
                'sentiment_check': sentiment_check,
                'message': f'Buy order was ALLOWED - Market sentiment is {sentiment_level}'
            }

        except Exception as e:
            # Check if this is a sentiment-related block
            error_message = str(e)
            is_sentiment_block = 'market sentiment' in error_message.lower() or 'below neutral' in error_message.lower()

            return {
                'success': True,
                'buy_order_blocked': is_sentiment_block,
                'block_reason': error_message,
                'market_sentiment_level': sentiment_level,
                'sentiment_check': sentiment_check,
                'message': f'Buy order was BLOCKED - {error_message}' if is_sentiment_block else f'Buy order failed for other reason: {error_message}'
            }

    except Exception as e:
        print(f"❌ Error in test system-wide buy block: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'System-wide buy block test failed'
        }


#################################################
########  Database Connection Monitoring
#################################################
@apiDecorator.protectedRoute('/monitor/database_connections', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def monitorDatabaseConnections():
    """Monitor database connection pool status for debugging connection issues"""
    try:
        from chalicelib.Utils.database import get_connection_info, get_engine

        print("🔍 Checking database connection status...")

        # Get connection pool information
        connection_info = get_connection_info()

        # Get engine information
        engine = get_engine()
        pool = engine.pool

        # Additional pool statistics
        pool_stats = {
            'pool_class': str(type(pool).__name__),
            'pool_size_setting': getattr(pool, '_pool_size', 'unknown'),
            'max_overflow_setting': getattr(pool, '_max_overflow', 'unknown'),
            'timeout_setting': getattr(pool, '_timeout', 'unknown'),
            'recycle_setting': getattr(pool, '_recycle', 'unknown'),
        }

        # Test a simple connection
        connection_test = {"status": "unknown", "error": None}
        try:
            from chalicelib.Utils.database import get_db_session
            with get_db_session() as db:
                from sqlalchemy import text
                result = db.execute(text("SELECT 1 as test")).fetchone()
                connection_test = {"status": "success", "test_result": result[0] if result else None}
        except Exception as test_error:
            connection_test = {"status": "failed", "error": str(test_error)}

        return {
            'success': True,
            'connection_info': connection_info,
            'pool_stats': pool_stats,
            'connection_test': connection_test,
            'recommendations': {
                'current_pool_size': connection_info.get('pool_size', 'unknown'),
                'current_checked_out': connection_info.get('checked_out', 'unknown'),
                'warning': 'If checked_out approaches pool_size + max_overflow, you may experience timeouts',
                'optimization_tips': [
                    'Use context managers (with get_db_session()) for all database operations',
                    'Avoid keeping sessions open longer than necessary',
                    'Consider reducing pool_size and max_overflow for Lambda functions',
                    'Monitor RDS Proxy connection limits'
                ]
            },
            'message': 'Database connection monitoring completed'
        }

    except Exception as e:
        print(f"❌ Error in database connection monitoring: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Database connection monitoring failed'
        }


@apiDecorator.protectedRoute('/monitor/force_connection_cleanup', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def forceConnectionCleanup():
    """Force cleanup of all database connections (emergency use only)"""
    try:
        from chalicelib.Utils.database import force_close_all_connections, reset_connection_pool

        print("🚨 Force cleaning up all database connections...")

        # Force close all connections
        force_close_all_connections()

        # Reset the connection pool
        reset_connection_pool()

        print("✅ Connection cleanup completed")

        return {
            'success': True,
            'message': 'All database connections have been forcefully closed and pool reset',
            'warning': 'This is an emergency operation. Monitor system behavior after cleanup.'
        }

    except Exception as e:
        print(f"❌ Error in force connection cleanup: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Force connection cleanup failed'
        }


#################################################
########  Drip Marketing Campaign System
#################################################

@apiDecorator.protectedRoute('/marketing/drip-campaign/status', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDripCampaignStatus():
    """Get current status of the drip marketing campaign"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.campaign_scheduler import CampaignScheduler

            result = CampaignScheduler.get_campaign_status(db)
            return result

    except Exception as e:
        print(f"❌ Error getting drip campaign status: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get drip campaign status'
        }


@apiDecorator.protectedRoute('/marketing/contact-lists', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getContactLists():
    """Get available contact lists from database"""
    try:
        from chalicelib.Marketing.mailjet_manager import MailjetManager
        from chalicelib.Marketing.models import MailjetContactList
        from chalicelib.Utils.database import get_db_session

        with get_db_session() as session:
            contact_lists = session.query(MailjetContactList).filter(
                MailjetContactList.is_active == True
            ).all()

            lists_data = []
            for contact_list in contact_lists:
                lists_data.append({
                    'id': contact_list.mailjet_list_id,
                    'name': contact_list.list_name,
                    'type': contact_list.list_type,
                    'description': contact_list.description,
                    'created_at': contact_list.created_at.isoformat() if contact_list.created_at else None
                })

            return {
                'success': True,
                'contact_lists': lists_data,
                'total_count': len(lists_data)
            }

    except Exception as e:
        print(f"❌ Error getting contact lists: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get contact lists'
        }


@apiDecorator.protectedRoute('/marketing/contact-lists/mailjet', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getMailjetContactLists():
    """Get all contact lists directly from Mailjet"""
    try:
        from chalicelib.Mailjet.serializer import MailjetSerializer
        from chalicelib.Utils.database import get_db_session
        from chalicelib.Marketing.models import MailjetContactList
        from mailjet_rest import Client

        # Get Mailjet credentials
        api_key, api_secret = MailjetSerializer.getAuth()
        mailjet = Client(auth=(api_key, api_secret), version='v3')

        print("📋 Fetching contact lists from Mailjet...")

        # Get all contact lists from Mailjet
        result = mailjet.contactslist.get()

        if result.status_code != 200:
            return {
                'success': False,
                'error': f'Mailjet API error: {result.status_code}',
                'message': 'Failed to fetch contact lists from Mailjet'
            }

        mailjet_data = result.json()
        mailjet_lists = mailjet_data.get('Data', [])

        print(f"📋 Found {len(mailjet_lists)} contact lists in Mailjet")

        # Get existing lists from database to mark which are already imported
        with get_db_session() as session:
            existing_lists = session.query(MailjetContactList).filter(
                MailjetContactList.is_active == True
            ).all()
            existing_ids = {cl.mailjet_list_id for cl in existing_lists}

        # Format the lists for frontend
        formatted_lists = []
        for mailjet_list in mailjet_lists:
            list_id = mailjet_list.get('ID')
            list_name = mailjet_list.get('Name', 'Unnamed List')
            subscriber_count = mailjet_list.get('SubscriberCount', 0)
            created_at = mailjet_list.get('CreatedAt', '')

            # Get list statistics
            try:
                stats = MailjetSerializer.getCampaignListStats(list_id)
                active_contacts = stats.get('active_contacts', 0)
                total_contacts = stats.get('total_contacts', subscriber_count)
            except:
                active_contacts = 0
                total_contacts = subscriber_count

            formatted_lists.append({
                'id': list_id,
                'name': list_name,
                'subscriber_count': subscriber_count,
                'active_contacts': active_contacts,
                'total_contacts': total_contacts,
                'created_at': created_at,
                'is_imported': list_id in existing_ids,
                'is_ready_for_campaigns': active_contacts > 0
            })

        # Sort by subscriber count (most contacts first)
        formatted_lists.sort(key=lambda x: x['subscriber_count'], reverse=True)

        return {
            'success': True,
            'mailjet_lists': formatted_lists,
            'count': len(formatted_lists),
            'imported_count': len(existing_ids)
        }

    except Exception as e:
        print(f"❌ Error getting Mailjet contact lists: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get Mailjet contact lists'
        }


@apiDecorator.protectedRoute('/marketing/contact-lists/import', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def importContactLists():
    """Import selected contact lists from Mailjet into database"""
    try:
        data = app.current_request.json_body or {}
        list_ids = data.get('list_ids', [])

        if not list_ids:
            return {
                'success': False,
                'error': 'No contact list IDs provided',
                'message': 'Please select at least one contact list to import'
            }

        from chalicelib.Mailjet.serializer import MailjetSerializer
        from chalicelib.Utils.database import get_db_session
        from chalicelib.Marketing.models import MailjetContactList
        from mailjet_rest import Client
        from datetime import datetime

        # Get Mailjet credentials
        api_key, api_secret = MailjetSerializer.getAuth()
        mailjet = Client(auth=(api_key, api_secret), version='v3')

        imported_lists = []
        failed_imports = []

        print(f"📥 Importing {len(list_ids)} contact lists from Mailjet...")

        for list_id in list_ids:
            try:
                # Get list details from Mailjet
                list_result = mailjet.contactslist.get(id=list_id)

                if list_result.status_code != 200:
                    failed_imports.append({
                        'list_id': list_id,
                        'error': f'Failed to fetch list details: HTTP {list_result.status_code}'
                    })
                    continue

                list_data = list_result.json()
                if not list_data.get('Data'):
                    failed_imports.append({
                        'list_id': list_id,
                        'error': 'List not found in Mailjet'
                    })
                    continue

                mailjet_list = list_data['Data'][0]
                list_name = mailjet_list.get('Name', f'Imported List {list_id}')
                created_at_str = mailjet_list.get('CreatedAt', '')

                # Parse creation date
                try:
                    if created_at_str:
                        created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                    else:
                        created_at = datetime.utcnow()
                except:
                    created_at = datetime.utcnow()

                # Import into database
                with get_db_session() as session:
                    # Check if already exists
                    existing_list = session.query(MailjetContactList).filter(
                        MailjetContactList.mailjet_list_id == list_id
                    ).first()

                    if existing_list:
                        # Update existing list
                        existing_list.list_name = list_name
                        existing_list.is_active = True
                        existing_list.updated_at = datetime.utcnow()
                        session.commit()

                        imported_lists.append({
                            'list_id': list_id,
                            'name': list_name,
                            'action': 'updated'
                        })
                    else:
                        # Create new list
                        new_list = MailjetContactList(
                            mailjet_list_id=list_id,
                            list_name=list_name,
                            list_type='imported',
                            description=f'Imported from Mailjet on {datetime.utcnow().strftime("%Y-%m-%d")}',
                            created_at=created_at
                        )
                        session.add(new_list)
                        session.commit()

                        imported_lists.append({
                            'list_id': list_id,
                            'name': list_name,
                            'action': 'created'
                        })

                print(f"✅ Imported list {list_id}: {list_name}")

            except Exception as e:
                print(f"❌ Failed to import list {list_id}: {str(e)}")
                failed_imports.append({
                    'list_id': list_id,
                    'error': str(e)
                })

        return {
            'success': True,
            'imported_lists': imported_lists,
            'failed_imports': failed_imports,
            'imported_count': len(imported_lists),
            'failed_count': len(failed_imports),
            'message': f'Successfully imported {len(imported_lists)} contact lists'
        }

    except Exception as e:
        print(f"❌ Error importing contact lists: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to import contact lists'
        }


@apiDecorator.protectedRoute('/marketing/contact-lists/remove', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def removeContactLists():
    """Remove selected contact lists from database (soft delete)"""
    try:
        data = app.current_request.json_body or {}
        list_ids = data.get('list_ids', [])

        if not list_ids:
            return {
                'success': False,
                'error': 'No contact list IDs provided',
                'message': 'Please select at least one contact list to remove'
            }

        from chalicelib.Utils.database import get_db_session
        from chalicelib.Marketing.models import MailjetContactList
        from datetime import datetime

        removed_lists = []
        failed_removals = []

        print(f"🗑️ Removing {len(list_ids)} contact lists from database...")

        for list_id in list_ids:
            try:
                with get_db_session() as session:
                    # Find the contact list in database
                    contact_list = session.query(MailjetContactList).filter(
                        MailjetContactList.mailjet_list_id == list_id,
                        MailjetContactList.is_active == True
                    ).first()

                    if contact_list:
                        # Soft delete - mark as inactive
                        contact_list.is_active = False
                        contact_list.updated_at = datetime.utcnow()
                        session.commit()

                        removed_lists.append({
                            'list_id': list_id,
                            'name': contact_list.list_name,
                            'action': 'removed'
                        })

                        print(f"✅ Removed list {list_id}: {contact_list.list_name}")
                    else:
                        failed_removals.append({
                            'list_id': list_id,
                            'error': 'Contact list not found in database'
                        })
                        print(f"⚠️ List {list_id} not found in database")

            except Exception as e:
                print(f"❌ Failed to remove list {list_id}: {str(e)}")
                failed_removals.append({
                    'list_id': list_id,
                    'error': str(e)
                })

        return {
            'success': True,
            'removed_lists': removed_lists,
            'failed_removals': failed_removals,
            'removed_count': len(removed_lists),
            'failed_count': len(failed_removals),
            'message': f'Successfully removed {len(removed_lists)} contact lists from database'
        }

    except Exception as e:
        print(f"❌ Error removing contact lists: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to remove contact lists'
        }


@apiDecorator.protectedRoute('/marketing/drip-campaign/sync', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def syncDripCampaign():
    """Manually sync the drip campaign contact list"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.drip_campaign_service import DripCampaignService

            result = DripCampaignService.perform_full_sync(db)
            return result

    except Exception as e:
        print(f"❌ Error syncing drip campaign: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to sync drip campaign'
        }


@apiDecorator.protectedRoute('/marketing/drip-campaign/send', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def sendDripCampaign():
    """Send a drip campaign email - always uses synchronous processing"""
    try:
        # Get optional parameters
        data = app.current_request.json_body or {}
        template_name = data.get('template_name')
        force_send = data.get('force_send', False)
        skip_sync = data.get('skip_sync', False)

        # Always use synchronous processing since Mailjet contact lists are fast
        print("📧 Using synchronous drip campaign processing")
        from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
        try:
            with quick_session() as db:
                app.current_request.db = db
                from chalicelib.Marketing.campaign_scheduler import CampaignScheduler

                result = CampaignScheduler.send_drip_campaign(
                    db,
                    template_name=template_name,
                    force_send=force_send,
                    use_sync_sending=False,  # Use standard Mailjet sending (async on Mailjet side)
                    skip_sync=skip_sync  # Use parameter from request
                )

                # Add environment info to response
                if result.get('success'):
                    result['processing_mode'] = 'synchronous'
                    result['environment'] = os.environ.get('STAGE', 'production')

                return result

        except Exception as e:
            print(f"❌ Error sending drip campaign synchronously: {str(e)}")
            import traceback
            traceback.print_exc()
            emergency_cleanup()
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to send drip campaign synchronously',
                'environment': os.environ.get('STAGE', 'production')
            }

    except Exception as e:
        print(f"❌ Error queuing drip campaign: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to queue drip campaign'
        }


# SQS Event Handler for Asynchronous Campaign Processing - DISABLED (using synchronous processing)
# @app.on_sqs_message(queue='thriving-campaign-processing')
# def process_campaign_queue(event):
#     """Process campaign messages from SQS queue - DISABLED"""
#     # This handler is no longer needed since we switched to synchronous processing
#     pass


# Additional endpoints for async campaign management - DISABLED (using synchronous processing)
# @apiDecorator.protectedRoute('/marketing/campaign-queue/stats', methods=['GET'], cors=corsConfig)
# def getCampaignQueueStats():
#     """Get campaign queue statistics - DISABLED"""
#     return {
#         'success': False,
#         'message': 'SQS queue management disabled - using synchronous processing'
#     }


# @apiDecorator.protectedRoute('/marketing/campaign-queue/create', methods=['POST'], cors=corsConfig)
# def createCampaignQueue():
#     """Create the campaign processing queue if it doesn't exist - DISABLED"""
#     return {
#         'success': False,
#         'message': 'SQS queue management disabled - using synchronous processing'
#     }


@apiDecorator.protectedRoute('/marketing/drip-campaign/send-manual', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def sendManualCampaign():
    """Send a manual campaign to a specific contact list - always uses synchronous processing"""
    try:
        data = app.current_request.json_body or {}

        template_name = data.get('template_name')
        contact_list_id = data.get('contact_list_id')
        force_send = data.get('force_send', False)
        custom_subject = data.get('custom_subject')
        custom_variables = data.get('custom_variables', {})

        # Validate required parameters
        if not template_name:
            return {
                'success': False,
                'error': 'Template name is required',
                'message': 'Please provide a template name'
            }

        if not contact_list_id:
            return {
                'success': False,
                'error': 'Contact list ID is required',
                'message': 'Please provide a contact list ID'
            }

        # Always use synchronous processing since Mailjet contact lists are fast
        print(f"📧 Sending manual campaign to contact list {contact_list_id} using template {template_name}")
        print(f"🔍 DEBUG: Manual campaign - contact_list_id: {contact_list_id} (type: {type(contact_list_id)})")
        print(f"🔍 DEBUG: Manual campaign - template_name: {template_name}")
        print(f"🔍 DEBUG: Manual campaign - Environment STAGE: {os.environ.get('STAGE', 'not_set')}")
        from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
        from chalicelib.Mailjet.serializer import MailjetSerializer
        from chalicelib.Marketing.email_templates import ThrivingEmailTemplates

        try:
            # Get template data
            templates = ThrivingEmailTemplates.get_all_templates()
            if template_name not in templates:
                return {
                    'success': False,
                    'error': f'Template "{template_name}" not found',
                    'message': f'Available templates: {", ".join(templates.keys())}'
                }

            template_data = templates[template_name]

            # Get or create template in Mailjet using the correct method
            from chalicelib.Marketing.campaign_scheduler import CampaignScheduler
            template_result = CampaignScheduler.create_mailjet_template_db(
                template_name=template_name,
                template_data=template_data,
                force_recreate=False
            )

            if not template_result.get('success'):
                return {
                    'success': False,
                    'error': f'Failed to get/create template: {template_result.get("error", "Unknown error")}',
                    'message': 'Template creation failed'
                }

            template_id = template_result['template_id']

            # Send campaign using Mailjet
            print(f"🔍 DEBUG: About to call sendCampaignEmail with list_id: {int(contact_list_id)}")
            campaign_result = MailjetSerializer.sendCampaignEmail(
                list_id=int(contact_list_id),
                template_id=template_id,
                subject=custom_subject or template_data['subject'],
                from_email="<EMAIL>",
                from_name="The Thriving Team",
                variables=custom_variables
            )

            # The sendCampaignEmail method throws exceptions on failure, so if we get here it succeeded
            return {
                'success': True,
                'message': 'Manual campaign sent successfully',
                'template_name': template_name,
                'template_id': template_id,
                'contact_list_id': contact_list_id,
                'campaign_id': campaign_result.get('campaign_id') if isinstance(campaign_result, dict) else None,
                'processing_mode': 'synchronous',
                'environment': os.environ.get('STAGE', 'production'),
                'force_send': force_send,
                'mailjet_result': campaign_result
            }

        except Exception as e:
            print(f"❌ Error sending manual campaign: {str(e)}")
            import traceback
            traceback.print_exc()
            emergency_cleanup()

            # Check if this is the "no active contacts" error
            error_message = str(e)
            if "active,subscribed recipient" in error_message or "no active contacts" in error_message.lower():
                return {
                    'success': False,
                    'error': 'Contact list has no active subscribers',
                    'message': f'The selected contact list (ID: {contact_list_id}) has no active, subscribed contacts. Please add contacts to this list or select a different list with active subscribers.',
                    'error_type': 'empty_contact_list',
                    'contact_list_id': contact_list_id,
                    'suggestions': [
                        'Add contacts to this contact list in Mailjet dashboard',
                        'Select a different contact list with active contacts',
                        'Create a test contact list with your email address'
                    ],
                    'environment': os.environ.get('STAGE', 'production')
                }
            else:
                return {
                    'success': False,
                    'error': str(e),
                    'message': 'Failed to send manual campaign',
                    'environment': os.environ.get('STAGE', 'production')
                }

    except Exception as e:
        print(f"❌ Error processing manual campaign: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to process manual campaign'
        }


@apiDecorator.protectedRoute('/marketing/campaign/stats/{campaign_id}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getCampaignStats(campaign_id):
    """Get delivery statistics for a specific campaign"""
    try:
        from chalicelib.Marketing.campaign_analytics import CampaignAnalytics

        print(f"📊 Getting campaign stats for campaign ID: {campaign_id}")

        # Convert campaign_id to int
        try:
            campaign_id = int(campaign_id)
        except ValueError:
            return {
                'success': False,
                'error': 'Invalid campaign ID - must be a number'
            }

        result = CampaignAnalytics.get_mailjet_campaign_stats(campaign_id)
        return result

    except Exception as e:
        print(f"❌ Error getting campaign stats: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get campaign statistics'
        }


@apiDecorator.protectedRoute('/marketing/campaign/click-stats/{campaign_id}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getCampaignClickStats(campaign_id):
    """Get click statistics for a specific campaign"""
    try:
        from chalicelib.Marketing.campaign_analytics import CampaignAnalytics

        print(f"🔗 Getting campaign click stats for campaign ID: {campaign_id}")

        # Convert campaign_id to int
        try:
            campaign_id = int(campaign_id)
        except ValueError:
            return {
                'success': False,
                'error': 'Invalid campaign ID - must be a number'
            }

        result = CampaignAnalytics.get_mailjet_campaign_click_stats(campaign_id)
        return result

    except Exception as e:
        print(f"❌ Error getting campaign click stats: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get campaign click statistics'
        }


@apiDecorator.protectedRoute('/marketing/campaign/comprehensive-stats/{campaign_id}', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getComprehensiveCampaignStats(campaign_id):
    """Get comprehensive statistics (delivery + clicks) for a specific campaign"""
    try:
        from chalicelib.Marketing.campaign_analytics import CampaignAnalytics

        print(f"📈 Getting comprehensive campaign stats for campaign ID: {campaign_id}")

        # Convert campaign_id to int
        try:
            campaign_id = int(campaign_id)
        except ValueError:
            return {
                'success': False,
                'error': 'Invalid campaign ID - must be a number'
            }

        result = CampaignAnalytics.get_comprehensive_campaign_stats(campaign_id)

        # If this is a new campaign with no stats yet, return 200 with helpful message
        if result.get('is_new_campaign', False):
            return {
                'success': True,
                'campaign_id': campaign_id,
                'message': 'Campaign statistics are not available yet. Please wait a few minutes and try again.',
                'is_new_campaign': True,
                'delivery_stats': result.get('delivery_stats', {}),
                'click_stats': result.get('click_stats', {}),
                'timestamp': result.get('timestamp')
            }

        return result

    except Exception as e:
        print(f"❌ Error getting comprehensive campaign stats: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get comprehensive campaign statistics'
        }


@apiDecorator.protectedRoute('/marketing/mailjet/test', methods=['POST'], cors=corsConfig)
def testMailjetConnection():
    """Test Mailjet connection and send a test email"""
    try:
        from chalicelib.Mailjet.serializer import MailjetSerializer
        from mailjet_rest import Client

        # Get request data
        data = app.current_request.json_body or {}
        send_test_email = data.get('send_email', True)
        test_email = data.get('email', '<EMAIL>')

        # Test basic connection using our improved method
        print(f"🔍 Testing Mailjet connection...")
        connection_test = MailjetSerializer.testConnection()
        print(f"🔍 Connection test result: {connection_test}")

        # Return detailed connection test results even if it fails
        response = {
            'success': connection_test['success'],
            'connection_test': connection_test
        }

        if not connection_test['success']:
            response['error'] = f'Mailjet connection test failed: {connection_test.get("error", connection_test.get("message"))}'
            return response

        # Test authentication
        api_key, api_secret = MailjetSerializer.getAuth()
        print(f"Testing Mailjet with API key: {api_key[:10]}...")

        # Test basic connection
        mailjet_v3 = Client(auth=(api_key, api_secret), version='v3')
        mailjet_v31 = Client(auth=(api_key, api_secret), version='v3.1')

        # Try to get account info
        print(f"🔍 Testing Mailjet user.get() call...")
        try:
            result = mailjet_v3.user.get()
            print(f"🔍 User.get() status: {result.status_code}")
            print(f"🔍 User.get() text: {result.text}")
        except Exception as e:
            print(f"❌ Exception in user.get(): {str(e)}")
            return {
                'success': False,
                'error': f'Exception calling Mailjet user.get(): {str(e)}'
            }

        if result.status_code != 200:
            try:
                error_response = result.json()
            except:
                error_response = result.text
            return {
                'success': False,
                'error': f'Mailjet API returned status {result.status_code}',
                'response': error_response
            }

        try:
            user_data = result.json()
            print(f"🔍 User data parsed successfully: {user_data}")
        except Exception as e:
            print(f"❌ Failed to parse user data: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to parse Mailjet response: {str(e)}',
                'response_text': result.text
            }
        response = {
            'success': True,
            'message': 'Mailjet connection successful',
            'api_key_prefix': api_key[:10] + '...',
            'account_info': {
                'username': user_data['Data'][0].get('Username', 'N/A'),
                'email': user_data['Data'][0].get('Email', 'N/A')
            },
            'campaign_list_test': {}
        }

        # Test campaign list functionality
        print(f"🔍 Testing campaign list functionality...")
        try:
            test_list_name = f"Test Campaign List {int(__import__('time').time())}"

            # Test finding non-existent list
            find_result = MailjetSerializer.findCampaignListByName(test_list_name)
            print(f"🔍 Find non-existent list result: {find_result}")

            # Test creating campaign list
            create_result = MailjetSerializer.createCampaignList(test_list_name)
            print(f"🔍 Create campaign list result: {create_result}")

            response['campaign_list_test'] = {
                'find_non_existent': find_result,
                'create_list': create_result,
                'success': create_result.get('success', False)
            }

            if create_result.get('success'):
                response['message'] += ' and campaign list test passed'
            else:
                response['message'] += ' but campaign list test failed'

        except Exception as e:
            print(f"❌ Campaign list test failed: {str(e)}")
            response['campaign_list_test'] = {
                'success': False,
                'error': str(e)
            }
            response['message'] += ' but campaign list test failed'

        # Send test email if requested
        if send_test_email:
            print(f"📧 Sending test email to: {test_email}")

            # First <NAME_EMAIL> is verified
            print(f"🔍 Checking sender verification...")
            try:
                sender_result = mailjet_v3.sender.get()
                print(f"🔍 Sender verification response: {sender_result.json()}")
            except Exception as e:
                print(f"⚠️ Could not check sender verification: {e}")

            # Use v3.1 API format (same as existing serializer)
            mail_data = {
                'Messages': [
                    {
                        "From": {
                            "Email": "<EMAIL>",
                            "Name": "Thriving Test"
                        },
                        "To": [
                            {
                                "Email": test_email,
                                "Name": "Test Recipient"
                            }
                        ],
                        "Subject": "Thriving Email System Test",
                        "TextPart": "This is a test email from the Thriving email system.",
                        "HTMLPart": f"""
                        <h3>Thriving Email System Test</h3>
                        <p>This is a test email from the Thriving email system.</p>
                        <p>If you received this, the Mailjet integration is working correctly!</p>
                        <p>Sent at: {__import__('datetime').datetime.now()}</p>
                        """
                    }
                ]
            }

            print(f"📧 Sending email with data: {mail_data}")
            email_result = mailjet_v31.send.create(data=mail_data)
            print(email_result.__dict__)
            print(f"📧 Email result status: {email_result.status_code}")
            print(f"📧 Email result text: {email_result.text}")

            try:
                email_response = email_result.json()
                print(f"📧 Email response parsed: {email_response}")
            except Exception as e:
                print(f"❌ Failed to parse email response: {str(e)}")
                response['test_email_sent'] = False
                response['email_error'] = f'Failed to parse email response: {str(e)} - Raw response: {email_result.text}'
                return response

            if email_result.status_code == 200:
                response['test_email_sent'] = True
                response['test_email_to'] = test_email
                response['message'] += f' and test email sent to {test_email}'

                # Extract message ID from v3.1 API response
                try:
                    messages = email_response.get('Messages', [])
                    if messages and len(messages) > 0:
                        to_list = messages[0].get('To', [])
                        if to_list and len(to_list) > 0:
                            response['email_message_id'] = to_list[0].get('MessageID')
                except Exception as e:
                    print(f"Warning: Could not extract message ID: {e}")
            else:
                response['test_email_sent'] = False
                response['email_error'] = f'Failed to send test email (status {email_result.status_code}): {email_response}'

        return response

    except Exception as e:
        print(f"❌ Error testing Mailjet connection: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to test Mailjet connection'
        }


# @apiDecorator.protectedRoute('/marketing/campaign-queue/process-test', methods=['POST'], cors=corsConfig)
# def processQueueTest():
#     """Test endpoint to manually process messages from the campaign queue - DISABLED"""
#     return {
#         'success': False,
#         'message': 'SQS queue testing disabled - using synchronous processing'
#     }

# Commented out SQS test function body:
"""
def processQueueTest_DISABLED():
    try:
        import boto3
        from chalicelib.Marketing.campaign_queue_service import CampaignQueueService
        from chalicelib.Marketing.async_campaign_processor import AsyncCampaignProcessor

        # Get SQS client and queue URL
        sqs = boto3.client('sqs')
        queue_url = CampaignQueueService._get_queue_url()

        print(f"🔍 Attempting to receive messages from queue: {queue_url}")

        # First try to receive messages normally
        response = sqs.receive_message(
            QueueUrl=queue_url,
            MaxNumberOfMessages=10,  # Try to get more messages
            WaitTimeSeconds=5,
            VisibilityTimeout=30  # Short visibility timeout for testing
        )

        messages = response.get('Messages', [])

        if not messages:
            return {
                'success': True,
                'message': 'No messages in queue to process',
                'processed_count': 0
            }

        processed_count = 0
        results = []

        for message in messages:
            try:
                # Process the message
                message_body = message['Body']
                receipt_handle = message['ReceiptHandle']

                print(f"Processing message: {message_body}")

                # Process the campaign
                result = AsyncCampaignProcessor.process_campaign_message(message_body)

                if result['success']:
                    # Delete the message from queue if processing was successful
                    sqs.delete_message(
                        QueueUrl=queue_url,
                        ReceiptHandle=receipt_handle
                    )
                    print(f"✅ Message processed and deleted from queue")
                    processed_count += 1
                else:
                    print(f"❌ Message processing failed: {result}")

                results.append({
                    'message_id': message.get('MessageId'),
                    'processing_result': result
                })

            except Exception as e:
                print(f"❌ Error processing message: {str(e)}")
                results.append({
                    'message_id': message.get('MessageId'),
                    'error': str(e)
                })

        return {
            'success': True,
            'message': f'Processed {processed_count} messages',
            'processed_count': processed_count,
            'total_messages': len(messages),
            'results': results
        }

    except Exception as e:
        print(f"❌ Error processing queue test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to process queue messages'
        }
"""


# @apiDecorator.protectedRoute('/marketing/campaign-queue/force-process', methods=['POST'], cors=corsConfig)
# def forceProcessStuckMessages():
#     """Force process stuck messages by purging the queue and sending a test campaign - DISABLED"""
#     return {
#         'success': False,
#         'message': 'SQS queue force processing disabled - using synchronous processing'
#     }

# Commented out force process function body:
"""
def forceProcessStuckMessages_DISABLED():
    try:
        import boto3
        from chalicelib.Marketing.campaign_queue_service import CampaignQueueService
        from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

        print("🚨 Force processing stuck messages...")

        # Get SQS client and queue URL
        sqs = boto3.client('sqs')
        queue_url = CampaignQueueService._get_queue_url()

        # Get current queue stats
        stats_response = sqs.get_queue_attributes(
            QueueUrl=queue_url,
            AttributeNames=[
                'ApproximateNumberOfMessages',
                'ApproximateNumberOfMessagesNotVisible',
                'ApproximateNumberOfMessagesDelayed'
            ]
        )

        attributes = stats_response['Attributes']
        messages_in_flight = int(attributes.get('ApproximateNumberOfMessagesNotVisible', 0))

        print(f"📊 Messages in flight before purge: {messages_in_flight}")

        # Purge the queue to clear stuck messages
        print("🧹 Purging queue to clear stuck messages...")
        sqs.purge_queue(QueueUrl=queue_url)

        # Wait a moment for purge to take effect
        import time
        time.sleep(2)

        # Send a test campaign directly through Mailjet to verify the system works
        print("📧 Sending test campaign directly...")

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.campaign_scheduler import CampaignScheduler

            # Send a test campaign synchronously
            result = CampaignScheduler.send_drip_campaign(
                db,
                template_name='welcome',
                force_send=True
            )

            return {
                'success': True,
                'message': 'Force processing completed',
                'stuck_messages_cleared': messages_in_flight,
                'test_campaign_result': result,
                'action_taken': 'Purged queue and sent test campaign directly'
            }

    except Exception as e:
        print(f"❌ Error force processing stuck messages: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to force process stuck messages'
        }
"""


@apiDecorator.protectedRoute('/marketing/mailjet/connection-test', methods=['GET'], cors=corsConfig)
def testMailjetConnectionOnly():
    """Simple Mailjet connection test without sending emails"""
    try:
        from chalicelib.Mailjet.serializer import MailjetSerializer

        print(f"🔍 Testing Mailjet connection (connection-only)...")
        connection_test = MailjetSerializer.testConnection()
        print(f"🔍 Connection test result: {connection_test}")

        return {
            'success': connection_test['success'],
            'connection_test': connection_test,
            'timestamp': __import__('datetime').datetime.now().isoformat()
        }

    except Exception as e:
        print(f"❌ Exception in Mailjet connection test: {str(e)}")
        print(__import__('traceback').format_exc())
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to test Mailjet connection'
        }


@apiDecorator.protectedRoute('/marketing/mailjet/send-test-email', methods=['POST'], cors=corsConfig)
def sendTestEmail():
    """Send a simple test email directly through Mailjet to verify email sending works"""
    try:
        from chalicelib.Mailjet.serializer import MailjetSerializer
        from mailjet_rest import Client

        # Get request data
        data = app.current_request.json_body or {}
        test_email = data.get('email', '<EMAIL>')

        print(f"📧 Sending test email to: {test_email}")

        # Test authentication
        api_key, api_secret = MailjetSerializer.getAuth()
        mailjet = Client(auth=(api_key, api_secret), version='v3.1')

        # Send test email
        mail_data = {
            'Messages': [{
                "From": {
                    "Email": "<EMAIL>",
                    "Name": "Thriving Test"
                },
                "To": [{
                    "Email": test_email,
                    "Name": "Test Recipient"
                }],
                "Subject": "Mailjet Test Email",
                "TextPart": "This is a test email from the Thriving billing system.",
                "HTMLPart": "<h3>Test Email</h3><p>This is a test email from the Thriving billing system.</p>"
            }]
        }

        result = mailjet.send.create(data=mail_data)

        if result.status_code == 200:
            return {
                'success': True,
                'message': f'Test email sent successfully to {test_email}',
                'status_code': result.status_code
            }
        else:
            return {
                'success': False,
                'message': f'Failed to send test email: {result.text}',
                'status_code': result.status_code
            }

    except Exception as e:
        print(f"❌ Send test email error: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to send test email'
        }


#################################################################
###########              Billing System      ###################
#################################################################

def require_superuser_access():
    """Check if current user has superuser access (user_level_id = 1)"""
    request_user = getattr(app.current_request, 'requestUser', None)
    if not request_user:
        raise APIException("Authentication required")

    # Get user_level_id from the correct structure (request_user.user.user_level_id)
    user_level_id = None
    if hasattr(request_user, 'user') and request_user.user:
        user_level_id = getattr(request_user.user, 'user_level_id', None)

    if user_level_id != 1:  # Super Admin only
        raise APIException("Superuser access required for billing operations")

    return request_user

@apiDecorator.protectedRoute('/billing/summary', methods=['GET'], cors=corsConfig)
def getBillingSummary():
    """Get billing summary/KPI data - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            # Get query parameters
            year = app.current_request.query_params.get('year')
            month = app.current_request.query_params.get('month')
            account_owner_id = app.current_request.query_params.get('account_owner_id')
            show_excluded_accounts = app.current_request.query_params.get('show_excluded_accounts')

            # Convert to integers if provided
            year = int(year) if year else None
            month = int(month) if month else None
            account_owner_id = int(account_owner_id) if account_owner_id else None
            show_excluded_accounts = show_excluded_accounts == 'true' if show_excluded_accounts else None

            # Get current user email
            current_user_email = getattr(request_user.user, 'email', None) if request_user.user else None

            response = BillingSerializer.get_billing_summary(db, year, month, account_owner_id, current_user_email, show_excluded_accounts)
            return response

    except Exception as e:
        print(f"❌ Billing summary endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/records', methods=['GET'], cors=corsConfig)
def getBillingRecords():
    """Get billing records with filtering - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            # Get query parameters
            year = app.current_request.query_params.get('year')
            month = app.current_request.query_params.get('month')
            account_owner_id = app.current_request.query_params.get('account_owner_id')
            status = app.current_request.query_params.get('status')
            limit = app.current_request.query_params.get('limit', '100')
            offset = app.current_request.query_params.get('offset', '0')
            show_excluded_accounts = app.current_request.query_params.get('show_excluded_accounts')

            # Convert to appropriate types
            year = int(year) if year else None
            month = int(month) if month else None
            account_owner_id = int(account_owner_id) if account_owner_id else None
            limit = int(limit)
            offset = int(offset)
            show_excluded_accounts = show_excluded_accounts == 'true' if show_excluded_accounts else None

            # Get current user email
            current_user_email = getattr(request_user.user, 'email', None) if request_user.user else None

            response = BillingSerializer.get_billing_records(
                db, year, month, account_owner_id, status, limit, offset, current_user_email, show_excluded_accounts
            )
            return response

    except Exception as e:
        print(f"❌ Billing records endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/generate', methods=['POST'], cors=corsConfig)
def generateBillingRecords():
    """Generate billing records for a specific month - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            data = app.current_request.json_body or {}
            year = data.get('year')
            month = data.get('month')
            account_owner_ids = data.get('account_owner_ids')  # [1, 77] or specific
            regenerate = data.get('regenerate', True)  # Default to True for regeneration
            show_excluded_accounts = data.get('show_excluded_accounts')

            if not year or not month:
                return {'success': False, 'message': 'Year and month are required'}

            # Get current user email
            current_user_email = getattr(request_user.user, 'email', None) if request_user.user else None

            response = BillingSerializer.generate_billing_records(
                db, year, month, account_owner_ids, request_user.user.id, regenerate, current_user_email, show_excluded_accounts
            )
            return response

    except Exception as e:
        print(f"❌ Generate billing records endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/mark-paid', methods=['POST'], cors=corsConfig)
def markBillingRecordPaid():
    """Mark a billing record as paid - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            data = app.current_request.json_body or {}
            billing_record_id = data.get('billing_record_id')
            payment_method = data.get('payment_method', 'manual')
            payment_reference = data.get('payment_reference')
            notes = data.get('notes')

            if not billing_record_id:
                return {'success': False, 'message': 'Billing record ID is required'}

            response = BillingSerializer.mark_as_paid(
                db, billing_record_id, request_user.user.id, payment_method, payment_reference, notes
            )
            return response

    except Exception as e:
        print(f"❌ Mark billing record paid endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/due-today', methods=['GET'], cors=corsConfig)
def getAccountsDueToday():
    """Get accounts with payments due today - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            account_owner_id = app.current_request.query_params.get('account_owner_id')
            account_owner_id = int(account_owner_id) if account_owner_id else None

            response = BillingSerializer.get_accounts_due_today(db, account_owner_id)
            return response

    except Exception as e:
        print(f"❌ Accounts due today endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/overdue', methods=['GET'], cors=corsConfig)
def getOverdueAccounts():
    """Get overdue accounts - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            account_owner_id = app.current_request.query_params.get('account_owner_id')
            account_owner_id = int(account_owner_id) if account_owner_id else None

            response = BillingSerializer.get_overdue_accounts(db, account_owner_id)
            return response

    except Exception as e:
        print(f"❌ Overdue accounts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/export', methods=['GET'], cors=corsConfig)
def exportBillingData():
    """Export billing data as CSV - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import csv
    import io
    from chalice import Response

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer

            # Get query parameters
            year = app.current_request.query_params.get('year')
            month = app.current_request.query_params.get('month')
            account_owner_id = app.current_request.query_params.get('account_owner_id')
            show_excluded_accounts = app.current_request.query_params.get('show_excluded_accounts')

            if not year or not month:
                return {'success': False, 'message': 'Year and month are required for export'}

            year = int(year)
            month = int(month)
            account_owner_id = int(account_owner_id) if account_owner_id else None
            show_excluded_accounts = show_excluded_accounts == 'true' if show_excluded_accounts else None

            # Get current user email
            current_user_email = getattr(request_user.user, 'email', None) if request_user.user else None

            # Get export data
            export_data = BillingSerializer.get_billing_export_data(db, year, month, account_owner_id, current_user_email, show_excluded_accounts)

            if not export_data:
                return {'success': False, 'message': 'No billing data found for the specified period'}

            # Create CSV content
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=export_data[0].keys())
            writer.writeheader()
            writer.writerows(export_data)
            csv_content = output.getvalue()
            output.close()

            # Determine platform name for filename
            platform_name = 'All'
            if account_owner_id == 1:
                platform_name = 'Thriving'
            elif account_owner_id == 77:
                platform_name = 'Innova'

            # Create filename
            filename = f"Billing_{platform_name}_{year}_{month:02d}.csv"

            # Return CSV response
            return Response(
                body=csv_content,
                status_code=200,
                headers={
                    'Content-Type': 'text/csv',
                    'Content-Disposition': f'attachment; filename="{filename}"'
                }
            )

    except Exception as e:
        print(f"❌ Export billing data endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None

@apiDecorator.protectedRoute('/billing/email', methods=['POST'], cors=corsConfig)
def emailBillingData():
    """Email billing data with CSV attachment - Superuser only"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    import csv
    import io
    import base64

    try:
        # Check superuser access
        request_user = require_superuser_access()

        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Billing.serializer import BillingSerializer
            from chalicelib.Mailjet.serializer import MailjetSerializer

            data = app.current_request.json_body or {}
            year = data.get('year')
            month = data.get('month')
            account_owner_id = data.get('account_owner_id')
            recipient_email = data.get('recipient_email', '<EMAIL>')
            custom_message = data.get('custom_message', '')
            show_excluded_accounts = data.get('show_excluded_accounts')

            if not year or not month:
                return {'success': False, 'message': 'Year and month are required'}

            year = int(year)
            month = int(month)
            account_owner_id = int(account_owner_id) if account_owner_id else None

            # Get current user email
            current_user_email = getattr(request_user.user, 'email', None) if request_user.user else None

            # Get export data
            export_data = BillingSerializer.get_billing_export_data(db, year, month, account_owner_id, current_user_email, show_excluded_accounts)

            if not export_data:
                return {'success': False, 'message': 'No billing data found for the specified period'}

            # Create CSV content
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=export_data[0].keys())
            writer.writeheader()
            writer.writerows(export_data)
            csv_content = output.getvalue()
            output.close()

            # Determine platform name
            platform_name = 'All Platforms'
            if account_owner_id == 1:
                platform_name = 'Thriving'
            elif account_owner_id == 77:
                platform_name = 'Innova'

            # Create filename
            filename = f"Billing_{platform_name.replace(' ', '_')}_{year}_{month:02d}.csv"

            # Encode CSV for attachment
            csv_base64 = base64.b64encode(csv_content.encode('utf-8')).decode('utf-8')

            # Get billing summary for email content
            summary_response = BillingSerializer.get_billing_summary(db, year, month, account_owner_id)
            summary = summary_response.get('summary', {})

            # Create email content
            email_subject = f"Billing Report - {platform_name} - {year}-{month:02d}"

            email_body = f"""
            <h2>Billing Report for {platform_name}</h2>
            <p><strong>Period:</strong> {year}-{month:02d}</p>

            <h3>Summary</h3>
            <ul>
                <li><strong>Total Accounts:</strong> {summary.get('total_accounts', 0)}</li>
                <li><strong>Total Amount Due:</strong> ${summary.get('total_amount_due', 0):,.2f}</li>
                <li><strong>Total Paid:</strong> ${summary.get('total_paid', 0):,.2f}</li>
                <li><strong>Total Overdue:</strong> ${summary.get('total_overdue', 0):,.2f}</li>
                <li><strong>Accounts Due Today:</strong> {summary.get('accounts_due_today', 0)}</li>
                <li><strong>Overdue Accounts:</strong> {summary.get('accounts_overdue', 0)}</li>
                <li><strong>Free Trial Accounts:</strong> {summary.get('free_trial_accounts', 0)}</li>
                <li><strong>Exempt Accounts:</strong> {summary.get('exempt_accounts', 0)}</li>
            </ul>

            {f'<h3>Additional Notes</h3><p>{custom_message}</p>' if custom_message else ''}

            <p>Please find the detailed billing report attached as a CSV file.</p>

            <hr>
            <p><small>Generated on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC by {request_user.first_name} {request_user.last_name}</small></p>
            """

            # Send email with attachment
            from mailjet_rest import Client
            api_key, api_secret = MailjetSerializer.getAuth()
            mailjet = Client(auth=(api_key, api_secret), version='v3.1')

            mail_data = {
                'Messages': [{
                    "From": {
                        "Email": "<EMAIL>",
                        "Name": "Thriving Billing System"
                    },
                    "To": [{
                        "Email": recipient_email,
                        "Name": "Billing Report Recipient"
                    }],
                    "Subject": email_subject,
                    "HTMLPart": email_body,
                    "Attachments": [{
                        "ContentType": "text/csv",
                        "Filename": filename,
                        "Base64Content": csv_base64
                    }]
                }]
            }

            email_result = mailjet.send.create(data=mail_data)

            if email_result.status_code == 200:
                return {
                    'success': True,
                    'message': f'Billing report emailed successfully to {recipient_email}',
                    'filename': filename,
                    'records_count': len(export_data),
                    'summary': summary
                }
            else:
                return {
                    'success': False,
                    'message': f'Failed to send email: {email_result.text}',
                    'status_code': email_result.status_code
                }

    except Exception as e:
        print(f"❌ Email billing data endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/marketing/drip-campaign/accounts', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDripCampaignAccounts():
    """Get accounts targeted by the drip campaign"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.drip_campaign_service import DripCampaignService

            accounts = DripCampaignService.get_thriving_accounts_without_automations(db)

            return {
                'success': True,
                'accounts': accounts,
                'total_count': len(accounts),
                'message': f'Found {len(accounts)} Thriving accounts without active automations'
            }

    except Exception as e:
        print(f"❌ Error getting drip campaign accounts: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get drip campaign accounts'
        }


# Scheduled drip campaign execution (runs twice weekly)
@app.schedule(Cron('0', '10', '?', '*', '2,5', '*'))  # Tuesdays and Fridays at 10:00 AM UTC
def scheduledDripCampaign(event):
    """
    Scheduled drip marketing campaign execution.
    Runs twice weekly (Tuesdays and Fridays) to send targeted emails to Thriving accounts without automations.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Marketing.campaign_scheduler import CampaignScheduler

    print("📧 Starting scheduled drip marketing campaign...")

    try:
        with quick_session() as db:
            result = CampaignScheduler.execute_scheduled_campaign(db)

            if result['success']:
                print(f"✅ Drip campaign executed successfully")
                if result.get('campaign_sent'):
                    print(f"📊 Campaign stats: {result.get('contacts_count', 0)} contacts reached")
                    print(f"📧 Template used: {result.get('template_used', 'N/A')}")
                else:
                    print(f"ℹ️ Campaign not sent: {result.get('message', 'Unknown reason')}")
            else:
                print(f"❌ Drip campaign failed: {result.get('error', 'Unknown error')}")

            return result

    except Exception as e:
        print(f"❌ Scheduled drip campaign error: {e}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


# Hook into account creation to add to drip campaign
def handle_new_account_for_drip_campaign(account_data):
    """
    Handle new account creation for drip campaign
    This should be called when a new account is created
    """
    try:
        from chalicelib.Utils.lambda_session import quick_session
        from chalicelib.Marketing.drip_campaign_service import DripCampaignService

        with quick_session() as db:
            result = DripCampaignService.handle_new_account_created(db, account_data)
            print(f"📧 Drip campaign account handling: {result['message']}")
            return result

    except Exception as e:
        print(f"❌ Error handling new account for drip campaign: {str(e)}")
        return {'success': False, 'error': str(e)}


# Hook into automation activation/deactivation
def handle_automation_change_for_drip_campaign(account_id, automation_data, action='activated'):
    """
    Handle automation activation/deactivation for drip campaign
    This should be called when automations are activated or deactivated
    """
    try:
        from chalicelib.Utils.lambda_session import quick_session
        from chalicelib.Marketing.drip_campaign_service import DripCampaignService

        with quick_session() as db:
            if action == 'activated':
                result = DripCampaignService.handle_automation_activated(db, account_id, automation_data)
            elif action == 'deactivated':
                result = DripCampaignService.handle_automation_deactivated(db, account_id, automation_data)
            else:
                return {'success': False, 'error': 'Invalid action'}

            print(f"📧 Drip campaign automation handling ({action}): {result['message']}")
            return result

    except Exception as e:
        print(f"❌ Error handling automation change for drip campaign: {str(e)}")
        return {'success': False, 'error': str(e)}


@apiDecorator.protectedRoute('/marketing/drip-campaign/analytics', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDripCampaignAnalytics():
    """Get analytics and performance data for drip campaigns"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.campaign_analytics import CampaignAnalytics

            # Get optional days parameter
            days = int(app.current_request.query_params.get('days', 30))

            result = CampaignAnalytics.get_campaign_performance_summary(db, days=days)
            return result

    except Exception as e:
        print(f"❌ Error getting drip campaign analytics: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get drip campaign analytics'
        }


@apiDecorator.protectedRoute('/marketing/drip-campaign/health', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getDripCampaignHealth():
    """Get health monitoring data for the drip campaign system"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.campaign_analytics import CampaignAnalytics

            result = CampaignAnalytics.monitor_campaign_health(db)
            return result

    except Exception as e:
        print(f"❌ Error getting drip campaign health: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to get drip campaign health'
        }

@apiDecorator.protectedRoute('/marketing/mailjet/templates', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def listMailjetTemplates():
    """List all available Mailjet email templates for debugging"""
    try:
        from chalicelib.Mailjet.serializer import MailjetSerializer

        result = MailjetSerializer.listAvailableTemplates()
        return result

    except Exception as e:
        print(f"❌ Error listing Mailjet templates: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to list Mailjet templates'
        }


@apiDecorator.protectedRoute('/marketing/drip-campaign/debug', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def debugDripCampaign():
    """Debug endpoint to check drip campaign data"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup

    try:
        with quick_session() as db:
            app.current_request.db = db
            from chalicelib.Marketing.drip_campaign_service import DripCampaignService
            from chalicelib.Mailjet.serializer import MailjetSerializer

            # Get debug info first
            from chalicelib.Accounts.models import Account, AccountOwner
            from chalicelib.Users.models import User
            from chalicelib.Bots.models import TradingBot
            from sqlalchemy import func

            total_accounts = db.query(Account).count()
            thriving_accounts = db.query(Account).join(
                AccountOwner, AccountOwner.account_id == Account.id
            ).filter(AccountOwner.owner_account_id == 1).count()

            active_thriving_accounts = db.query(Account).join(
                AccountOwner, AccountOwner.account_id == Account.id
            ).filter(
                AccountOwner.owner_account_id == 1,
                Account.status_id.in_([1, 2, 3, 7])
            ).count()

            thriving_with_users = db.query(Account).join(
                AccountOwner, AccountOwner.account_id == Account.id
            ).join(
                User, User.account_id == Account.id
            ).filter(
                AccountOwner.owner_account_id == 1,
                Account.status_id.in_([1, 2, 3, 7]),
                User.status_id == 11  # Corrected active user status ID
            ).count()

            # Check what user statuses exist
            user_statuses = db.query(User.status_id, func.count(User.id)).join(
                Account, Account.id == User.account_id
            ).join(
                AccountOwner, AccountOwner.account_id == Account.id
            ).filter(
                AccountOwner.owner_account_id == 1,
                Account.status_id.in_([1, 2, 3, 7])
            ).group_by(User.status_id).all()

            # Try without user status restriction
            thriving_with_any_users = db.query(Account).join(
                AccountOwner, AccountOwner.account_id == Account.id
            ).join(
                User, User.account_id == Account.id
            ).filter(
                AccountOwner.owner_account_id == 1,
                Account.status_id.in_([1, 2, 3, 7])
            ).count()

            # Get accounts without automations
            accounts = DripCampaignService.get_thriving_accounts_without_automations(db)

            # Check campaign list
            list_info = DripCampaignService.ensure_campaign_list_exists()

            # Get Mailjet list contacts if list exists
            mailjet_contacts = []
            if list_info.get('list_id'):
                mailjet_result = MailjetSerializer.getCampaignListContacts(list_info['list_id'])
                if mailjet_result['success']:
                    mailjet_contacts = mailjet_result['contacts']

            return {
                'success': True,
                'debug_info': {
                    'database_counts': {
                        'total_accounts': total_accounts,
                        'thriving_accounts': thriving_accounts,
                        'active_thriving_accounts': active_thriving_accounts,
                        'thriving_with_users': thriving_with_users
                    },
                    'database_accounts': {
                        'count': len(accounts),
                        'accounts': accounts[:5]  # First 5 for debugging
                    },
                    'campaign_list': list_info,
                    'mailjet_contacts': {
                        'count': len(mailjet_contacts),
                        'contacts': mailjet_contacts[:5]  # First 5 for debugging
                    }
                }
            }

    except Exception as e:
        print(f"❌ Error debugging drip campaign: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to debug drip campaign'
        }

@apiDecorator.protectedRoute('/marketing/drip-campaign/template/preview', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def previewDripCampaignTemplate():
    """Preview a drip campaign email template with sample data"""
    try:
        from chalicelib.Marketing.email_templates import ThrivingEmailTemplates

        # Get template name from query parameters
        template_name = app.current_request.query_params.get('template', 'welcome')

        # Sample data for template rendering
        from datetime import datetime
        current_year = datetime.now().year

        sample_data = {
            'first_name': 'John',
            'last_name': 'Smith',
            'account_name': 'John Smith',
            'email': '<EMAIL>',
            'current_year': str(current_year)
        }

        # Get the template
        template_methods = {
            'welcome': ThrivingEmailTemplates.get_welcome_template,
            'benefits': ThrivingEmailTemplates.get_benefits_template,
            'success_stories': ThrivingEmailTemplates.get_success_stories_template,
            'urgency': ThrivingEmailTemplates.get_urgency_template,
            'automation_benefits': ThrivingEmailTemplates.get_automation_benefits_template,
            'portfolio_customization': ThrivingEmailTemplates.get_portfolio_customization_template,
            'market_expectations': ThrivingEmailTemplates.get_market_expectations_template,
            'risk_management': ThrivingEmailTemplates.get_risk_management_template,
            'getting_started': ThrivingEmailTemplates.get_getting_started_template,
            'pricing_overview': ThrivingEmailTemplates.get_pricing_overview_template,
            'pricing_value': ThrivingEmailTemplates.get_pricing_value_template,
            'pricing_comparison': ThrivingEmailTemplates.get_pricing_comparison_template,
            'portfolio_builder_intro': ThrivingEmailTemplates.get_portfolio_builder_intro_template,
            'portfolio_builder_features': ThrivingEmailTemplates.get_portfolio_builder_features_template,
            'portfolio_builder_results': ThrivingEmailTemplates.get_portfolio_builder_results_template
        }

        if template_name not in template_methods:
            return {
                'success': False,
                'error': f'Template "{template_name}" not found',
                'available_templates': list(template_methods.keys())
            }

        # Get template data
        template_data = template_methods[template_name]()

        # Render template with sample data
        rendered_html = template_data['html_content']
        rendered_subject = template_data['subject']

        # Replace template variables with sample data
        for variable in template_data.get('variables', []):
            if variable in sample_data:
                rendered_html = rendered_html.replace(f'{{{{ {variable} }}}}', sample_data[variable])
                rendered_subject = rendered_subject.replace(f'{{{{ {variable} }}}}', sample_data[variable])

        return {
            'success': True,
            'template_name': template_name,
            'subject': rendered_subject,
            'html_content': rendered_html,
            'sample_data': sample_data,
            'variables': template_data.get('variables', [])
        }

    except Exception as e:
        print(f"❌ Error previewing template: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'message': 'Failed to preview template'
        }


#################################################################
###########           Social Media System     ###################
#################################################################

@apiDecorator.protectedRoute('/admin/social-media/settings', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaSettings():
    """Get social media settings for current account owner"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.get_settings(app)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaSettings endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/settings', content_types=['application/json'], methods=['PUT'], cors=corsConfig)
def updateSocialMediaSettings():
    """Update social media settings"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer
    from chalicelib.SocialMedia.schemas import SETTINGS_UPDATE_SCHEMA
    from chalicelib.Utils.functions import validateSchema

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            # Validate request data
            validateSchema(data, SETTINGS_UPDATE_SCHEMA)

            response = SocialMediaSerializer.update_settings(data, app)
            return response
    except Exception as e:
        print(f"❌ UpdateSocialMediaSettings endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/accounts', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaAccounts():
    """Get connected social media accounts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.get_accounts(app)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaAccounts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/accounts', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def connectSocialMediaAccount():
    """Connect a new social media account"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer
    from chalicelib.SocialMedia.schemas import CONNECT_ACCOUNT_SCHEMA
    from chalicelib.Utils.functions import validateSchema

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            # Validate request data
            validateSchema(data, CONNECT_ACCOUNT_SCHEMA)

            response = SocialMediaSerializer.connect_account(data, app)
            return response
    except Exception as e:
        print(f"❌ ConnectSocialMediaAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/accounts/{account_id}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def disconnectSocialMediaAccount(account_id):
    """Disconnect a social media account"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.disconnect_account(int(account_id), app)
            return response
    except Exception as e:
        print(f"❌ DisconnectSocialMediaAccount endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/detect-gains', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def detectAndQueueGainsPosts():
    """Detect trading gains and queue social media posts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.detect_and_queue_gains_posts(app)
            return response
    except Exception as e:
        print(f"❌ DetectAndQueueGainsPosts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/process-queue', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def processPostQueue():
    """Process queued social media posts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.process_post_queue(app)
            return response
    except Exception as e:
        print(f"❌ ProcessPostQueue endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/posts', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaPosts():
    """Get social media posts with filtering"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db

            # Get query parameters
            query_params = app.current_request.query_params or {}
            limit = int(query_params.get('limit', 50))
            offset = int(query_params.get('offset', 0))
            platform = query_params.get('platform')
            post_type = query_params.get('post_type')
            status = query_params.get('status')

            response = SocialMediaSerializer.get_posts(app, limit, offset, platform, post_type, status)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaPosts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/accounts/{account_id}/refresh', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def refreshSocialMediaToken(account_id):
    """Refresh access token for a social media account"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.refresh_token(int(account_id), app)
            return response
    except Exception as e:
        print(f"❌ RefreshSocialMediaToken endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/token-status', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaTokenStatus():
    """Get token expiration status for all social media accounts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.get_token_status(app)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaTokenStatus endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/analytics', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaAnalytics():
    """Get social media analytics and performance metrics"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db

            # Get query parameters
            query_params = app.current_request.query_params or {}
            platform = query_params.get('platform')
            post_type = query_params.get('post_type')
            days = int(query_params.get('days', 30))

            response = SocialMediaSerializer.get_analytics(app, platform, post_type, days)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaAnalytics endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/analytics/summary', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaAnalyticsSummary():
    """Get social media analytics summary dashboard"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db

            # Get query parameters
            query_params = app.current_request.query_params or {}
            days = int(query_params.get('days', 30))

            response = SocialMediaSerializer.get_analytics_summary(app, days)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaAnalyticsSummary endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/posts/{post_id}/analytics', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def updatePostAnalytics(post_id):
    """Update analytics data for a specific post"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            response = SocialMediaSerializer.update_post_analytics(post_id, data, app)
            return response
    except Exception as e:
        print(f"❌ UpdatePostAnalytics endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/generate-video', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def generateSocialMediaVideo():
    """Generate AI video content for social media posts"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            content_type = data.get('content_type', 'marketing')
            platform = data.get('platform', 'facebook')
            video_data = data.get('data', {})

            response = SocialMediaSerializer.generate_video_content(app, content_type, video_data, platform)
            return response
    except Exception as e:
        print(f"❌ GenerateSocialMediaVideo endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/posts/with-video', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createSocialMediaPostWithVideo():
    """Create social media post with AI-generated video content"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            response = SocialMediaSerializer.create_post_with_video(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateSocialMediaPostWithVideo endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app.current_request, 'db'):
            app.current_request.db = None





@apiDecorator.protectedRoute('/admin/social-media/posts/{post_id}/post', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def postToSocialMediaPlatform(post_id):
    """Manually post to social media platform"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.post_to_platform(int(post_id), app)
            return response
    except Exception as e:
        print(f"❌ PostToSocialMediaPlatform endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/posts', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createManualSocialMediaPost():
    """Create a manual social media post"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer
    from chalicelib.SocialMedia.schemas import CREATE_POST_SCHEMA
    from chalicelib.Utils.functions import validateSchema

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            # Validate request data
            validateSchema(data, CREATE_POST_SCHEMA)

            response = SocialMediaSerializer.create_manual_post(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateManualSocialMediaPost endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/posts/{post_id}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteSocialMediaPost(post_id):
    """Delete a social media post from both database and platform"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.delete_post(int(post_id), app)
            return response
    except Exception as e:
        print(f"❌ DeleteSocialMediaPost endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/posts/{post_id}/retry', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def retrySocialMediaPost(post_id):
    """Retry a failed social media post"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.retry_post(int(post_id), app)
            return response
    except Exception as e:
        print(f"❌ RetrySocialMediaPost endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/templates', content_types=['application/json'], methods=['GET'], cors=corsConfig)
def getSocialMediaTemplates():
    """Get social media post templates"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db

            # Get query parameters
            query_params = app.current_request.query_params or {}
            platform = query_params.get('platform')
            post_type = query_params.get('post_type')

            response = SocialMediaSerializer.get_templates(app, platform, post_type)
            return response
    except Exception as e:
        print(f"❌ GetSocialMediaTemplates endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/templates', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def createSocialMediaTemplate():
    """Create a new social media post template"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer
    from chalicelib.SocialMedia.schemas import TEMPLATE_SCHEMA
    from chalicelib.Utils.functions import validateSchema

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            # Validate request data
            validateSchema(data, TEMPLATE_SCHEMA)

            response = SocialMediaSerializer.create_template(data, app)
            return response
    except Exception as e:
        print(f"❌ CreateSocialMediaTemplate endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/templates/{template_id}', content_types=['application/json'], methods=['PUT'], cors=corsConfig)
def updateSocialMediaTemplate(template_id):
    """Update a social media post template"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer
    from chalicelib.SocialMedia.schemas import TEMPLATE_SCHEMA
    from chalicelib.Utils.functions import validateSchema

    try:
        with quick_session() as db:
            app.current_request.db = db
            data = app.current_request.json_body

            # Validate request data (partial validation for updates)
            # validateSchema(data, TEMPLATE_SCHEMA)

            response = SocialMediaSerializer.update_template(int(template_id), data, app)
            return response
    except Exception as e:
        print(f"❌ UpdateSocialMediaTemplate endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/templates/{template_id}', content_types=['application/json'], methods=['DELETE'], cors=corsConfig)
def deleteSocialMediaTemplate(template_id):
    """Delete a social media post template"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.delete_template(int(template_id), app)
            return response
    except Exception as e:
        print(f"❌ DeleteSocialMediaTemplate endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@apiDecorator.protectedRoute('/admin/social-media/schedule-marketing', content_types=['application/json'], methods=['POST'], cors=corsConfig)
def scheduleMarketingPosts():
    """Schedule marketing posts based on templates and settings"""
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            app.current_request.db = db
            response = SocialMediaSerializer.schedule_marketing_posts(app)
            return response
    except Exception as e:
        print(f"❌ ScheduleMarketingPosts endpoint error: {e}")
        emergency_cleanup()
        raise e
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


#################################################################
###########     Social Media Auto-Post Cron Jobs   #############
#################################################################

@app.schedule(Cron('*/30', '*', '?', '*', '*', '*'))  # Run every 30 minutes
def socialMediaGainsDetection(event):
    """
    Social media gains detection cron job that runs every 30 minutes.
    Detects trading gains >= 1% and queues social media posts.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            # Create a mock request object for the cron job
            app.current_request = ChaliceRequest()
            app.current_request.db = db

            print("🚀 Starting social media gains detection...")

            # Detect and queue gains posts
            result = SocialMediaSerializer.detect_and_queue_gains_posts(app)

            print(f"✅ Social media gains detection completed: {result}")

            return {
                'success': True,
                'result': result
            }

    except Exception as e:
        print(f"❌ Error in social media gains detection: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e)
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.schedule(Cron('*/15', '*', '?', '*', '*', '*'))  # Run every 15 minutes
def socialMediaPostProcessor(event):
    """
    Social media post processor cron job that runs every 15 minutes.
    Processes queued social media posts and creates actual posts.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            # Create a mock request object for the cron job
            app.current_request = ChaliceRequest()
            app.current_request.db = db

            print("📝 Starting social media post processing...")

            # Process queued posts
            result = SocialMediaSerializer.process_post_queue(app)

            print(f"✅ Social media post processing completed: {result}")

            return {
                'success': True,
                'result': result
            }

    except Exception as e:
        print(f"❌ Error in social media post processing: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e)
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None


@app.schedule(Cron('0', '*/6', '?', '*', '*', '*'))  # Run every 6 hours
def socialMediaMarketingScheduler(event):
    """
    Social media marketing scheduler cron job that runs every 6 hours.
    Schedules marketing posts based on templates and settings.
    """
    from chalicelib.Utils.lambda_session import quick_session, emergency_cleanup
    from chalicelib.Utils.functions import ChaliceRequest
    from chalicelib.SocialMedia.serializer import SocialMediaSerializer

    try:
        with quick_session() as db:
            # Create a mock request object for the cron job
            app.current_request = ChaliceRequest()
            app.current_request.db = db

            print("📅 Starting social media marketing post scheduling...")

            # Schedule marketing posts
            result = SocialMediaSerializer.schedule_marketing_posts(app)

            print(f"✅ Social media marketing scheduling completed: {result}")

            return {
                'success': True,
                'result': result
            }

    except Exception as e:
        print(f"❌ Error in social media marketing scheduling: {str(e)}")
        import traceback
        traceback.print_exc()
        emergency_cleanup()
        return {
            'success': False,
            'error': str(e)
        }
    finally:
        if hasattr(app, 'current_request') and hasattr(app.current_request, 'db'):
            app.current_request.db = None
